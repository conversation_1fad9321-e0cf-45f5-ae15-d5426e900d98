/* 横幅时钟样式 - 强制重置所有可能的干扰样式，支持全页面显示 */
#banner-clock {
  position: fixed !important;
  bottom: 20px !important;
  left: 20px !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border-radius: 15px !important;
  padding: 15px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  z-index: 1000 !important;
  min-width: 200px !important;
  max-width: 260px !important;
  width: auto !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  transition: all 0.3s ease !important;
  display: block !important;
  box-sizing: border-box !important;
  /* 强制重置可能的继承样式 */
  font-family: inherit !important;
  line-height: normal !important;
  text-align: left !important;
  color: #333 !important;
}

/* 确保在所有页面类型中都能正确显示 - 优化容器适配 */
#banner-clock[data-container="banners"] {
  /* 当添加到banners容器时，使用相对较低的z-index，保持在横幅内 */
  z-index: 1000 !important;
  position: fixed !important;
  /* 确保在横幅容器内时也能正确显示 */
}

#banner-clock[data-container="page-header"] {
  /* 当添加到page-header时，保持固定定位，不跟随页面滚动 */
  z-index: 1500 !important;
  position: fixed !important;
  /* 确保不被页面头部内容遮挡 */
}

#banner-clock[data-container="body-wrap"] {
  /* 当添加到body-wrap时，保持固定定位 */
  z-index: 1500 !important;
  position: fixed !important;
  /* 确保在页面包装内正确显示 */
}

#banner-clock[data-container="body"] {
  /* 当添加到body时，确保不被其他元素遮挡 */
  z-index: 9999 !important;
  position: fixed !important;
  /* 最高优先级，确保始终可见 */
}

/* 特殊情况：当时钟在非body容器中时，确保不会被容器的overflow属性影响 */
#banner-clock[data-container="banners"],
#banner-clock[data-container="page-header"],
#banner-clock[data-container="body-wrap"] {
  /* 使用fixed定位确保不受父容器限制 */
  position: fixed !important;
  /* 确保在视口中的固定位置 */
  bottom: 20px !important;
  left: 20px !important;
}

/* 深色主题适配 */
[data-theme="dark"] #banner-clock {
  background: rgba(45, 55, 72, 0.95) !important;
  border: 1px solid rgba(74, 85, 104, 0.3) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
  color: #e2e8f0 !important;
}

#banner-clock:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 深色主题悬停效果 */
[data-theme="dark"] #banner-clock:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
}

.banner-clock-time {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 5px;
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
}

.banner-clock-date {
  font-size: 0.9rem;
  color: #666;
  text-align: center;
  margin-bottom: 10px;
  font-weight: 500;
}

/* 深色主题 - 时间和日期 */
[data-theme="dark"] .banner-clock-time {
  color: #f7fafc !important;
}

[data-theme="dark"] .banner-clock-date {
  color: #a0aec0 !important;
}

/* 天气容器 - 最高优先级强制水平布局 */
#banner-clock .banner-clock-weather,
div#banner-clock .banner-clock-weather,
body #banner-clock .banner-clock-weather {
  display: flex !important;
  flex-direction: row !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 8px !important;
  gap: 8px !important;
  width: 100% !important;
  flex-wrap: nowrap !important;
  /* 强制重置可能的干扰样式 */
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
  overflow: visible !important;
  position: relative !important;
  float: none !important;
  clear: both !important;
}

/* 天气项目 - 最高优先级强制水平布局 */
#banner-clock .banner-clock-weather-item,
div#banner-clock .banner-clock-weather-item,
body #banner-clock .banner-clock-weather-item {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 3px !important;
  font-size: 0.8rem !important;
  color: #555 !important;
  flex: 0 1 auto !important;
  white-space: nowrap !important;
  min-width: fit-content !important;
  overflow: visible !important;
  /* 强制重置可能的干扰样式 */
  height: auto !important;
  width: auto !important;
  position: relative !important;
  float: none !important;
  clear: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

#banner-clock .banner-clock-weather-item span {
  overflow: visible !important;
  text-overflow: clip !important;
  white-space: nowrap !important;
  min-width: fit-content !important;
  flex-shrink: 0 !important;
}

#banner-clock .banner-clock-weather-item i {
  color: #007bff !important;
  font-size: 0.9rem !important;
}

/* 深色主题 - 天气元素 */
[data-theme="dark"] #banner-clock .banner-clock-weather-item {
  color: #e2e8f0 !important;
}

[data-theme="dark"] #banner-clock .banner-clock-weather-item i {
  color: #4dabf7 !important;
}

[data-theme="dark"] #banner-clock .banner-clock-weather-item span {
  color: #e2e8f0 !important;
}

.banner-clock-location {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 0.75rem;
  color: #777;
  cursor: pointer;
  transition: color 0.3s ease;
}

.banner-clock-location:hover {
  color: #007bff;
}

.banner-clock-location i {
  color: #dc3545;
  font-size: 0.8rem;
}

/* 深色模式适配 */
[data-theme="dark"] #banner-clock {
  background: rgba(30, 30, 30, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .banner-clock-time {
  color: #fff;
}

[data-theme="dark"] .banner-clock-date {
  color: #ccc;
}

[data-theme="dark"] .banner-clock-weather-item {
  color: #bbb;
}

[data-theme="dark"] .banner-clock-location {
  color: #999;
}

[data-theme="dark"] .banner-clock-location:hover {
  color: #4dabf7;
}

/* 移动端适配 */
@media (max-width: 768px) {
  #banner-clock {
    width: 180px !important;
    min-height: 130px !important;
    height: auto !important;
    bottom: 15px !important;
    left: 15px !important;
    padding: 12px !important;
    min-width: 180px !important;
  }

  .banner-clock-time {
    font-size: 1.2rem !important;
  }

  .banner-clock-date {
    font-size: 0.8rem !important;
  }

  .banner-clock-weather {
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between !important;
  }

  .banner-clock-weather-item {
    font-size: 0.7rem !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
  }

  .banner-clock-location {
    font-size: 0.7rem !important;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  #banner-clock {
    min-width: 180px !important;
    max-width: 240px !important;
    width: auto !important;
    min-height: 110px !important;
    height: auto !important;
    bottom: 10px !important;
    left: 10px !important;
    padding: 10px !important;
  }

  .banner-clock-time {
    font-size: 1.1rem !important;
  }

  .banner-clock-date {
    font-size: 0.75rem !important;
    margin-bottom: 8px !important;
  }

  .banner-clock-weather {
    margin-bottom: 6px !important;
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    gap: 8px !important;
  }

  .banner-clock-weather-item {
    font-size: 0.7rem !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    flex: 0 1 auto !important;
    min-width: fit-content !important;
  }

  .banner-clock-location {
    font-size: 0.65rem !important;
  }
}

/* 极小屏幕额外优化 */
@media (max-width: 360px) {
  #banner-clock {
    min-width: 170px !important;
    max-width: 220px !important;
    padding: 8px !important;
  }

  .banner-clock-weather-item {
    font-size: 0.65rem !important;
    gap: 2px !important;
  }

  .banner-clock-weather-item i {
    font-size: 0.8rem !important;
  }

  .banner-clock-weather {
    gap: 6px !important;
  }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
  #banner-clock {
    min-width: 160px !important;
    max-width: 220px !important;
    width: auto !important;
    min-height: 100px !important;
    height: auto !important;
    bottom: 10px !important;
    left: 10px !important;
    padding: 8px !important;
  }

  .banner-clock-time {
    font-size: 1rem !important;
    margin-bottom: 3px !important;
  }

  .banner-clock-date {
    font-size: 0.7rem !important;
    margin-bottom: 6px !important;
  }

  .banner-clock-weather {
    margin-bottom: 4px !important;
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    gap: 6px !important;
  }

  .banner-clock-weather-item {
    font-size: 0.65rem !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    flex: 0 1 auto !important;
    min-width: fit-content !important;
  }

  .banner-clock-location {
    font-size: 0.6rem !important;
  }
}

/* 位置选择器模态框样式 */
#location-selector-modal {
  display: none;
  position: fixed;
  z-index: 10000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
}

.location-modal-content {
  background-color: #fff;
  margin: 10% auto;
  padding: 0;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.location-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.location-modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
}

.location-close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.location-close-btn:hover {
  background-color: #f5f5f5;
  color: #333;
}

.location-modal-body {
  padding: 20px;
}

.location-search {
  position: relative;
  margin-bottom: 20px;
}

#city-search-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  outline: none;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

#city-search-input:focus {
  border-color: #007bff;
}

.city-suggestions {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-top: none;
  border-radius: 0 0 8px 8px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
}

.suggestion-item {
  padding: 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.suggestion-item:hover {
  background-color: #f8f9fa;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.popular-cities h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1rem;
}

.city-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 10px;
}

.city-btn {
  padding: 10px 15px;
  border: 2px solid #e0e0e0;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  color: #333;
}

.city-btn:hover {
  border-color: #007bff;
  background-color: #f8f9fa;
}

.city-btn.selected {
  border-color: #007bff;
  background-color: #007bff;
  color: white;
}

.location-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #eee;
}

.btn-cancel,
.btn-confirm {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.btn-cancel {
  background-color: #f8f9fa;
  color: #666;
}

.btn-cancel:hover {
  background-color: #e9ecef;
}

.btn-confirm {
  background-color: #007bff;
  color: white;
}

.btn-confirm:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-confirm:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* 深色模式适配 */
[data-theme="dark"] .location-modal-content {
  background-color: #2d3748;
  color: #e2e8f0;
}

[data-theme="dark"] .location-modal-header {
  border-bottom-color: #4a5568;
}

[data-theme="dark"] .location-modal-header h3 {
  color: #e2e8f0;
}

[data-theme="dark"] .location-close-btn {
  color: #a0aec0;
}

[data-theme="dark"] .location-close-btn:hover {
  background-color: #4a5568;
  color: #e2e8f0;
}

[data-theme="dark"] #city-search-input {
  background-color: #4a5568;
  border-color: #718096;
  color: #e2e8f0;
}

[data-theme="dark"] #city-search-input:focus {
  border-color: #4dabf7;
}

[data-theme="dark"] .city-suggestions {
  background-color: #4a5568;
  border-color: #718096;
}

[data-theme="dark"] .suggestion-item {
  border-bottom-color: #718096;
  color: #e2e8f0;
}

[data-theme="dark"] .suggestion-item:hover {
  background-color: #2d3748;
}

[data-theme="dark"] .popular-cities h4 {
  color: #e2e8f0;
}

[data-theme="dark"] .city-btn {
  background-color: #4a5568;
  border-color: #718096;
  color: #e2e8f0;
}

[data-theme="dark"] .city-btn:hover {
  border-color: #4dabf7;
  background-color: #2d3748;
}

[data-theme="dark"] .city-btn.selected {
  border-color: #4dabf7;
  background-color: #4dabf7;
  color: white;
}

[data-theme="dark"] .location-modal-footer {
  border-top-color: #4a5568;
}

[data-theme="dark"] .btn-cancel {
  background-color: #4a5568;
  color: #a0aec0;
}

[data-theme="dark"] .btn-cancel:hover {
  background-color: #2d3748;
}

[data-theme="dark"] .btn-confirm {
  background-color: #4dabf7;
}

[data-theme="dark"] .btn-confirm:hover:not(:disabled) {
  background-color: #3182ce;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

#banner-clock {
  animation: fadeInUp 0.5s ease-out;
}

/* 时间数字跳动效果 */
.banner-clock-time {
  transition: transform 0.1s ease;
}

.banner-clock-time.updating {
  transform: scale(1.05);
}
