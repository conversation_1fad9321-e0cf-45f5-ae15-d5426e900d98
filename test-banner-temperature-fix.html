<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>横幅温度信息显示优化测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="source/css/banner-clock.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .test-button {
            margin: 5px;
            padding: 8px 16px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .test-button:hover {
            background: #005a87;
        }
        
        .screen-size-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 2000;
        }
        
        .theme-toggle {
            position: fixed;
            top: 10px;
            left: 10px;
            background: #007cba;
            color: white;
            border: none;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
            z-index: 2000;
        }
    </style>
</head>
<body>
    <div class="screen-size-info" id="screen-info">
        屏幕尺寸: <span id="screen-size">--</span>
    </div>
    
    <button class="theme-toggle" onclick="toggleTheme()">🌙 切换主题</button>
    
    <div class="test-container">
        <h1>🌡️ 横幅温度信息显示优化测试</h1>
        
        <div class="test-section">
            <h3>📱 测试说明</h3>
            <p>此页面用于测试横幅时钟中温度信息显示不全的问题修复效果。</p>
            <ul>
                <li><strong>优化内容：</strong>增加容器宽度、优化文字显示、改进响应式布局</li>
                <li><strong>测试方法：</strong>调整浏览器窗口大小，观察左下角横幅时钟的温度显示</li>
                <li><strong>预期效果：</strong>温度信息完整显示，不被截断</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🧪 测试控制</h3>
            <button class="test-button" onclick="updateWeatherData('晴', '25', '65')">测试短天气 (晴 25°)</button>
            <button class="test-button" onclick="updateWeatherData('多云', '18', '72')">测试中等天气 (多云 18°)</button>
            <button class="test-button" onclick="updateWeatherData('雷阵雨', '22', '85')">测试长天气 (雷阵雨 22°)</button>
            <button class="test-button" onclick="updateWeatherData('大到暴雨', '16', '95')">测试极长天气 (大到暴雨 16°)</button>
            <button class="test-button" onclick="updateWeatherData('晴', '-5', '45')">测试负温度 (晴 -5°)</button>
            <button class="test-button" onclick="updateWeatherData('多云', '35', '88')">测试高温度 (多云 35°)</button>
        </div>
        
        <div class="test-section">
            <h3>📏 屏幕尺寸测试</h3>
            <p>当前屏幕宽度: <span id="current-width">--</span>px</p>
            <p>响应式断点:</p>
            <ul>
                <li>360px以下: 极小屏幕优化</li>
                <li>480px以下: 超小屏幕优化</li>
                <li>768px以下横屏: 横屏模式优化</li>
                <li>正常屏幕: 标准显示</li>
            </ul>
        </div>
    </div>

    <!-- 横幅时钟容器 -->
    <div id="banners"></div>

    <script>
        // 创建横幅时钟
        function createBannerClock() {
            const banners = document.getElementById('banners');
            if (banners && !document.getElementById('banner-clock')) {
                const bannerClock = document.createElement('div');
                bannerClock.id = 'banner-clock';
                bannerClock.innerHTML = `
                    <div class="banner-clock-time">21:50:30</div>
                    <div class="banner-clock-date">7月21日星期一</div>
                    <div class="banner-clock-weather">
                        <div class="banner-clock-weather-item">
                            <i class="fas fa-cloud-sun"></i>
                            <span>多云 25°</span>
                        </div>
                        <div class="banner-clock-weather-item">
                            <i class="fas fa-tint"></i>
                            <span>68%</span>
                        </div>
                    </div>
                    <div class="banner-clock-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>宿迁市</span>
                    </div>
                `;
                banners.appendChild(bannerClock);
                console.log('✅ 横幅时钟已创建');
            }
        }

        // 更新天气数据
        function updateWeatherData(weather, temperature, humidity) {
            const weatherEl = document.querySelector('.banner-clock-weather');
            if (weatherEl) {
                // 模拟优化后的显示格式
                const displayText = weather.length > 2 ? weather.substring(0, 2) : weather;
                const weatherIcon = getWeatherIcon(weather);
                
                weatherEl.innerHTML = `
                    <div class="banner-clock-weather-item">
                        <i class="${weatherIcon}"></i>
                        <span>${displayText} ${temperature}°</span>
                    </div>
                    <div class="banner-clock-weather-item">
                        <i class="fas fa-tint"></i>
                        <span>${humidity}%</span>
                    </div>
                `;
                console.log(`🌤️ 天气数据已更新: ${weather} ${temperature}°C ${humidity}%`);
            }
        }

        // 获取天气图标
        function getWeatherIcon(weather) {
            const iconMap = {
                '晴': 'fas fa-sun',
                '多云': 'fas fa-cloud-sun',
                '阴': 'fas fa-cloud',
                '雨': 'fas fa-cloud-rain',
                '雪': 'fas fa-snowflake',
                '雾': 'fas fa-smog',
                '霾': 'fas fa-smog',
                '小雨': 'fas fa-cloud-rain',
                '中雨': 'fas fa-cloud-rain',
                '大雨': 'fas fa-cloud-rain',
                '暴雨': 'fas fa-cloud-rain',
                '雷阵雨': 'fas fa-bolt',
                '大到暴雨': 'fas fa-cloud-rain'
            };
            return iconMap[weather] || 'fas fa-cloud-sun';
        }

        // 切换主题
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            console.log(`🎨 主题已切换到: ${newTheme}`);
        }

        // 更新屏幕尺寸信息
        function updateScreenInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            document.getElementById('screen-size').textContent = `${width}x${height}`;
            document.getElementById('current-width').textContent = width;
            
            // 显示当前断点
            let breakpoint = '正常屏幕';
            if (width <= 360) breakpoint = '极小屏幕 (≤360px)';
            else if (width <= 480) breakpoint = '超小屏幕 (≤480px)';
            else if (width <= 768 && window.matchMedia('(orientation: landscape)').matches) {
                breakpoint = '横屏模式 (≤768px)';
            }
            
            document.getElementById('screen-info').innerHTML = `
                屏幕尺寸: ${width}x${height}<br>
                断点: ${breakpoint}
            `;
        }

        // 更新时间
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleTimeString('zh-CN');
            const dateStr = now.toLocaleDateString('zh-CN', {
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            });
            
            const timeEl = document.querySelector('.banner-clock-time');
            const dateEl = document.querySelector('.banner-clock-date');
            
            if (timeEl) timeEl.textContent = timeStr;
            if (dateEl) dateEl.textContent = dateStr;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            createBannerClock();
            updateScreenInfo();
            updateTime();
            
            // 定时更新时间
            setInterval(updateTime, 1000);
            
            // 监听窗口大小变化
            window.addEventListener('resize', updateScreenInfo);
            
            console.log('🚀 测试页面已加载完成');
        });
    </script>
</body>
</html>
