<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banner Clock Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="source/css/banner-clock.css">
    <link rel="stylesheet" href="source/css/mobile-fix-gentle.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        /* 模拟Solitude主题的实际约束 */
        #banners {
            width: 100%;
            height: 100vh;
            background-image: url('https://picsum.photos/1920/1080');
            background-size: cover;
            background-position: center;
            position: relative;
            /* 模拟可能的宽度限制 */
            max-width: 1200px;
            margin: 0 auto;
        }

        /* 模拟移动端约束 */
        @media (max-width: 768px) {
            #banners {
                max-width: 100%;
                padding: 0 15px;
                box-sizing: border-box;
            }
        }

        /* 模拟超小屏幕约束 */
        @media (max-width: 480px) {
            #banners {
                padding: 0 10px;
            }
        }
        
        .test-info {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        [data-theme="dark"] body {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }
        
        [data-theme="dark"] .test-info {
            background: rgba(30, 30, 30, 0.9);
            color: white;
        }
        
        [data-theme="dark"] .theme-toggle {
            background: rgba(30, 30, 30, 0.9);
            color: white;
        }

        /* 模拟可能的CSS冲突 */
        * {
            box-sizing: border-box;
        }

        div {
            /* 这些样式可能会干扰布局 */
        }

        /* 测试固定高度的影响 */
        .test-fixed-height #banner-clock {
            height: 120px;
        }
    </style>
</head>
<body>
    <div id="banners">
        <div class="test-info">
            <h1>Banner Clock Component Test</h1>
            <p>横幅时钟组件测试页面</p>
            <p>时钟应该显示在左下角</p>
        </div>
    </div>
    
    <button class="theme-toggle" onclick="toggleTheme()">
        <i class="fas fa-moon"></i> 切换主题
    </button>

    <button class="theme-toggle" onclick="toggleFixedHeight()" style="top: 70px;">
        <i class="fas fa-arrows-alt-v"></i> 测试固定高度
    </button>

    <!-- Mock weather manager for testing -->
    <script>
        // Mock weather manager
        window.weatherManager = {
            onUpdate: function(callback) {
                // Simulate weather data update after 2 seconds
                setTimeout(() => {
                    callback({
                        weather: '晴',
                        temperature: '25°C',
                        humidity: '65%',
                        city: '测试城市'
                    });
                }, 2000);
            }
        };
        
        // Theme toggle function
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);

            const icon = document.querySelector('.theme-toggle i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        // Test fixed height function
        function toggleFixedHeight() {
            const body = document.body;
            if (body.classList.contains('test-fixed-height')) {
                body.classList.remove('test-fixed-height');
                console.log('移除固定高度限制');
            } else {
                body.classList.add('test-fixed-height');
                console.log('添加固定高度限制');
            }
        }
        
        // Initialize banner clock when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            // Create banner clock element
            const banners = document.getElementById('banners');
            if (banners && !document.getElementById('banner-clock')) {
                const bannerClock = document.createElement('div');
                bannerClock.id = 'banner-clock';
                bannerClock.innerHTML = `
                    <div class="banner-clock-time">加载中...</div>
                    <div class="banner-clock-date">加载中...</div>
                    <div class="banner-clock-weather">
                        <div class="banner-clock-weather-item">
                            <i class="fas fa-cloud-sun"></i>
                            <span>获取天气中...</span>
                        </div>
                        <div class="banner-clock-weather-item">
                            <i class="fas fa-tint"></i>
                            <span>--</span>
                        </div>
                    </div>
                    <div class="banner-clock-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>获取位置中...</span>
                    </div>
                `;
                banners.appendChild(bannerClock);
            }
        });
    </script>
    
    <script src="source/js/banner-clock.js"></script>
</body>
</html>
