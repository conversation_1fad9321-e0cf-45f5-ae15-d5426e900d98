<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>横幅时钟调试工具</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="source/css/banner-clock.css">
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #00ff00;
            font-size: 12px;
        }
        
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 20px;
        }
        
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 3px;
            background: #111;
        }
        
        .debug-title {
            color: #ffff00;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .debug-log {
            height: 200px;
            overflow-y: auto;
            background: #000;
            border: 1px solid #333;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        
        .debug-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        
        .debug-btn {
            padding: 8px 12px;
            border: 1px solid #00ff00;
            background: #000;
            color: #00ff00;
            cursor: pointer;
            font-family: inherit;
            font-size: 11px;
        }
        
        .debug-btn:hover {
            background: #003300;
        }
        
        .debug-btn.danger {
            border-color: #ff0000;
            color: #ff0000;
        }
        
        .debug-btn.danger:hover {
            background: #330000;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        
        .status-item {
            padding: 10px;
            border: 1px solid #333;
            background: #111;
        }
        
        .status-label {
            color: #888;
            font-size: 10px;
        }
        
        .status-value {
            color: #00ff00;
            font-weight: bold;
        }
        
        .status-error {
            color: #ff0000;
        }
        
        .status-warning {
            color: #ffff00;
        }
        
        /* 模拟banners容器 */
        #banners {
            width: 100%;
            height: 100px;
            background: linear-gradient(45deg, #333, #666);
            border: 1px solid #555;
            position: relative;
            margin: 20px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 14px;
        }
        
        .banners-hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 style="color: #ffff00; text-align: center;">🔍 横幅时钟调试工具</h1>
        
        <div class="debug-section">
            <div class="debug-title">🎮 控制面板</div>
            <div class="debug-controls">
                <button class="debug-btn" onclick="initClock()">初始化时钟</button>
                <button class="debug-btn" onclick="showHomePage()">显示首页</button>
                <button class="debug-btn" onclick="hideHomePage()">隐藏首页</button>
                <button class="debug-btn" onclick="simulatePjax()">模拟PJAX</button>
                <button class="debug-btn danger" onclick="destroyClock()">销毁时钟</button>
                <button class="debug-btn" onclick="clearLog()">清空日志</button>
            </div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">📊 实时状态</div>
            <div class="status-grid" id="status-grid">
                <!-- 状态项将动态生成 -->
            </div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">📝 调试日志</div>
            <div class="debug-log" id="debug-log"></div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">🎯 测试容器</div>
            <div id="banners">模拟横幅容器 (banners)</div>
        </div>
    </div>

    <script>
        // 日志系统
        const originalConsole = {
            log: console.log,
            warn: console.warn,
            error: console.error
        };
        
        function addToLog(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.join(' ');
            const logEl = document.getElementById('debug-log');
            const color = type === 'error' ? '#ff0000' : type === 'warn' ? '#ffff00' : '#00ff00';
            logEl.innerHTML += `<span style="color: #888">[${timestamp}]</span> <span style="color: ${color}">[${type.toUpperCase()}]</span> ${message}\n`;
            logEl.scrollTop = logEl.scrollHeight;
            
            // 调用原始console方法
            originalConsole[type](...args);
        }
        
        // 重写console方法
        console.log = (...args) => addToLog('log', ...args);
        console.warn = (...args) => addToLog('warn', ...args);
        console.error = (...args) => addToLog('error', ...args);
        
        // 模拟配置中的初始化函数（简化版）
        function initBannerClock() {
            console.log('🎯 开始初始化横幅时钟...');
            console.log('🔍 当前页面URL:', window.location.href);
            
            // 清理旧实例
            if (window.bannerClockInstance) {
                if (window.bannerClockInstance.destroy) {
                    window.bannerClockInstance.destroy();
                }
                window.bannerClockInstance = null;
                console.log('🗑️ 清理旧的横幅时钟实例');
            }

            // 移除旧DOM
            const existingClock = document.getElementById('banner-clock');
            if (existingClock) {
                existingClock.remove();
                console.log('🗑️ 移除旧的横幅时钟DOM');
            }

            // 等待DOM稳定
            setTimeout(() => {
                let targetContainer = document.getElementById('banners');
                let containerType = 'banners';
                
                console.log('🔍 DOM检查结果:');
                console.log('- banners容器:', targetContainer ? '✅ 存在' : '❌ 不存在');
                console.log('- body元素:', document.body ? '✅ 存在' : '❌ 不存在');
                
                if (!targetContainer) {
                    targetContainer = document.body;
                    containerType = 'body';
                    console.log('📍 banners容器未找到，将时钟添加到body');
                } else {
                    console.log('📍 找到banners容器，将时钟添加到banners');
                }
                
                if (!targetContainer) {
                    console.error('❌ 无法找到合适的容器，跳过时钟初始化');
                    return;
                }

                // 创建时钟DOM
                const bannerClock = document.createElement('div');
                bannerClock.id = 'banner-clock';
                bannerClock.setAttribute('data-container', containerType);
                bannerClock.innerHTML = `
                    <div class="banner-clock-time">调试模式</div>
                    <div class="banner-clock-date">${new Date().toLocaleDateString()}</div>
                    <div class="banner-clock-weather">
                        <div class="banner-clock-weather-item">
                            <i class="fas fa-bug"></i>
                            <span>调试中</span>
                        </div>
                    </div>
                    <div class="banner-clock-location">
                        <i class="fas fa-cog"></i>
                        <span>测试环境</span>
                    </div>
                `;
                
                targetContainer.appendChild(bannerClock);
                console.log(`📍 横幅时钟DOM已创建并添加到${containerType}容器`);

                // 模拟组件初始化
                setTimeout(() => {
                    window.bannerClockInstance = { 
                        destroy: () => console.log('🗑️ 模拟组件销毁'),
                        containerType: containerType
                    };
                    console.log('🚀 横幅时钟组件已初始化');
                }, 300);
            }, 100);
        }
        
        // 控制函数
        function initClock() {
            initBannerClock();
        }
        
        function showHomePage() {
            const banners = document.getElementById('banners');
            banners.classList.remove('banners-hidden');
            console.log('🏠 显示首页banners容器');
        }
        
        function hideHomePage() {
            const banners = document.getElementById('banners');
            banners.classList.add('banners-hidden');
            console.log('📄 隐藏首页banners容器');
        }
        
        function simulatePjax() {
            console.log('🔄 模拟PJAX导航开始...');
            
            // 模拟清理
            if (window.bannerClockInstance) {
                window.bannerClockInstance.destroy();
                window.bannerClockInstance = null;
            }
            const existingClock = document.getElementById('banner-clock');
            if (existingClock) {
                existingClock.remove();
            }
            
            // 随机切换页面状态
            setTimeout(() => {
                const isHomePage = Math.random() > 0.5;
                console.log(`🎯 导航到: ${isHomePage ? '首页' : '其他页面'}`);
                
                if (isHomePage) {
                    showHomePage();
                } else {
                    hideHomePage();
                }
                
                // 重新初始化
                setTimeout(() => {
                    console.log('✅ PJAX导航完成，重新初始化...');
                    initBannerClock();
                }, 200);
            }, 100);
        }
        
        function destroyClock() {
            if (window.bannerClockInstance) {
                window.bannerClockInstance.destroy();
                window.bannerClockInstance = null;
            }
            const clock = document.getElementById('banner-clock');
            if (clock) {
                clock.remove();
            }
            console.log('💥 时钟已销毁');
        }
        
        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }
        
        // 状态更新
        function updateStatus() {
            const statusGrid = document.getElementById('status-grid');
            const clock = document.getElementById('banner-clock');
            const banners = document.getElementById('banners');
            
            const statusItems = [
                {
                    label: '时钟DOM存在',
                    value: clock ? '✅ 是' : '❌ 否',
                    class: clock ? 'status-value' : 'status-error'
                },
                {
                    label: '时钟实例存在',
                    value: window.bannerClockInstance ? '✅ 是' : '❌ 否',
                    class: window.bannerClockInstance ? 'status-value' : 'status-error'
                },
                {
                    label: 'banners容器',
                    value: banners && !banners.classList.contains('banners-hidden') ? '✅ 可见' : '❌ 隐藏',
                    class: banners && !banners.classList.contains('banners-hidden') ? 'status-value' : 'status-warning'
                },
                {
                    label: '容器类型',
                    value: clock ? clock.getAttribute('data-container') || '未知' : '无',
                    class: 'status-value'
                },
                {
                    label: '父容器',
                    value: clock ? (clock.parentElement.tagName + (clock.parentElement.id ? '#' + clock.parentElement.id : '')) : '无',
                    class: 'status-value'
                },
                {
                    label: '当前时间',
                    value: new Date().toLocaleTimeString(),
                    class: 'status-value'
                }
            ];
            
            statusGrid.innerHTML = statusItems.map(item => `
                <div class="status-item">
                    <div class="status-label">${item.label}</div>
                    <div class="${item.class}">${item.value}</div>
                </div>
            `).join('');
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 调试工具加载完成');
            initBannerClock();
            
            // 定期更新状态
            setInterval(updateStatus, 1000);
        });
    </script>
</body>
</html>
