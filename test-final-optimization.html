<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>横幅时钟最终优化测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="source/css/banner-clock.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        [data-theme="dark"] body {
            background: #1a1a1a;
            color: #e2e8f0;
        }
        
        /* 导航栏 */
        #nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        [data-theme="dark"] #nav {
            background: rgba(26, 26, 26, 0.95);
        }
        
        .nav-link {
            color: #333;
            text-decoration: none;
            margin: 0 15px;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-link:hover {
            background: rgba(0, 123, 255, 0.1);
        }
        
        [data-theme="dark"] .nav-link {
            color: #e2e8f0;
        }
        
        /* 页面结构 */
        #body-wrap {
            min-height: 100vh;
            padding-top: 60px;
        }
        
        #page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        #banners {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 60px 40px;
            text-align: center;
            color: white;
            max-width: 600px;
            margin: 0 20px;
        }
        
        .banners-title-big {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .banners-title-small {
            font-size: 1.2rem;
            opacity: 0.8;
            margin-bottom: 30px;
        }
        
        /* 主内容区域 */
        #content-inner {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .content-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        [data-theme="dark"] .content-card {
            background: #2d3748;
        }
        
        /* 控制面板 */
        .control-panel {
            position: fixed;
            top: 80px;
            right: 20px;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            z-index: 2000;
            min-width: 200px;
        }
        
        [data-theme="dark"] .control-panel {
            background: #2d3748;
            color: #e2e8f0;
        }
        
        .control-btn {
            display: block;
            width: 100%;
            padding: 8px 12px;
            margin: 5px 0;
            border: 1px solid #007bff;
            background: #007bff;
            color: white;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            text-align: center;
            text-decoration: none;
        }
        
        .control-btn:hover {
            background: #0056b3;
        }
        
        .control-btn.active {
            background: #28a745;
            border-color: #28a745;
        }
        
        .status-info {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            font-size: 11px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        [data-theme="dark"] .status-info {
            background: #1a202c;
        }
        
        /* 页面模式样式 */
        .page-mode-home .page-mode-article,
        .page-mode-home .page-mode-tool { display: none; }
        
        .page-mode-article .page-mode-home,
        .page-mode-article .page-mode-tool { display: none; }
        .page-mode-article #banners { display: none; }
        
        .page-mode-tool .page-mode-home,
        .page-mode-tool .page-mode-article { display: none; }
        .page-mode-tool #banners { display: none; }
        .page-mode-tool #page-header { 
            min-height: 200px;
            background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
        }
    </style>
</head>
<body id="body" data-type="solitude" class="page-mode-home">
    <!-- 导航栏 -->
    <nav id="nav">
        <div>
            <strong>我的博客</strong>
        </div>
        <div>
            <a href="#" class="nav-link" onclick="switchToHomePage()">首页</a>
            <a href="#" class="nav-link" onclick="switchToArticlePage()">文章</a>
            <a href="#" class="nav-link" onclick="switchToToolPage()">在线工具</a>
        </div>
    </nav>
    
    <!-- 控制面板 -->
    <div class="control-panel">
        <h4 style="margin: 0 0 15px 0; font-size: 14px;">🎮 测试控制</h4>
        <button class="control-btn active" id="btn-home" onclick="switchToHomePage()">首页模式</button>
        <button class="control-btn" id="btn-article" onclick="switchToArticlePage()">文章页模式</button>
        <button class="control-btn" id="btn-tool" onclick="switchToToolPage()">工具页模式</button>
        <hr style="margin: 15px 0;">
        <button class="control-btn" onclick="toggleTheme()">切换主题</button>
        <button class="control-btn" onclick="forceReinit()">强制重新初始化</button>
        
        <div class="status-info" id="status-info">
            状态加载中...
        </div>
    </div>
    
    <!-- 页面结构 -->
    <div id="body-wrap">
        <!-- 页面头部 -->
        <header id="page-header">
            <!-- 首页横幅 -->
            <div class="page-mode-home">
                <div id="banners">
                    <div class="banners-title-big">欢迎来到我的博客</div>
                    <div class="banners-title-small">分享技术与生活的点点滴滴</div>
                </div>
            </div>
            
            <!-- 文章页头部 -->
            <div class="page-mode-article">
                <div style="text-align: center; color: white; padding: 40px;">
                    <h1>文章标题</h1>
                    <p>发布时间：2025年7月22日</p>
                </div>
            </div>
            
            <!-- 工具页头部 -->
            <div class="page-mode-tool">
                <div style="text-align: center; color: white; padding: 40px;">
                    <h1>在线工具</h1>
                    <p>实用的在线工具集合</p>
                </div>
            </div>
        </header>
        
        <!-- 主内容区域 -->
        <main id="content-inner">
            <!-- 首页内容 -->
            <div class="page-mode-home">
                <div class="content-card">
                    <h2>最新文章</h2>
                    <p>这里是首页的文章列表内容...</p>
                    <p>横幅时钟应该显示在左下角，位于banners容器内。</p>
                </div>
            </div>
            
            <!-- 文章页内容 -->
            <div class="page-mode-article">
                <div class="content-card">
                    <h2>文章内容</h2>
                    <p>这里是文章的详细内容...</p>
                    <p>横幅时钟应该显示在左下角，位于page-header容器内。</p>
                    <p>注意：此时banners容器不存在，时钟应该自动选择page-header容器。</p>
                </div>
            </div>
            
            <!-- 工具页内容 -->
            <div class="page-mode-tool">
                <div class="content-card">
                    <h2>参数保存工具</h2>
                    <p>这里是在线工具的内容...</p>
                    <p>横幅时钟应该显示在左下角，位于page-header容器内。</p>
                    <p>这模拟了您提到的"在线工具"页面场景。</p>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 优化后的初始化函数
        function initBannerClock() {
            console.log('🎯 开始初始化横幅时钟...');
            console.log('🔍 当前页面模式:', document.body.className);
            
            // 清理旧实例
            if (window.bannerClockInstance) {
                if (window.bannerClockInstance.destroy) {
                    window.bannerClockInstance.destroy();
                }
                window.bannerClockInstance = null;
                console.log('🗑️ 清理旧的横幅时钟实例');
            }

            // 移除旧DOM
            const existingClock = document.getElementById('banner-clock');
            if (existingClock) {
                existingClock.remove();
                console.log('🗑️ 移除旧的横幅时钟DOM');
            }

            // 等待DOM稳定
            setTimeout(() => {
                // 智能容器选择策略
                let targetContainer = null;
                let containerType = 'fixed';
                
                // 检查各种可能的容器
                const bannersContainer = document.getElementById('banners');
                const pageHeader = document.getElementById('page-header');
                const bodyWrap = document.getElementById('body-wrap');
                
                // 检查容器是否可见
                function isContainerVisible(element) {
                    if (!element) return false;
                    const style = window.getComputedStyle(element);
                    return style.display !== 'none' && style.visibility !== 'hidden' && element.offsetParent !== null;
                }
                
                console.log('🔍 DOM检查结果:');
                console.log('- banners容器:', bannersContainer ? (isContainerVisible(bannersContainer) ? '✅ 存在且可见' : '⚠️ 存在但隐藏') : '❌ 不存在');
                console.log('- page-header:', pageHeader ? (isContainerVisible(pageHeader) ? '✅ 存在且可见' : '⚠️ 存在但隐藏') : '❌ 不存在');
                console.log('- body-wrap:', bodyWrap ? (isContainerVisible(bodyWrap) ? '✅ 存在且可见' : '⚠️ 存在但隐藏') : '❌ 不存在');
                
                // 容器选择优先级（只选择可见的容器）
                if (bannersContainer && isContainerVisible(bannersContainer)) {
                    targetContainer = bannersContainer;
                    containerType = 'banners';
                    console.log('📍 使用banners容器（首页模式）');
                } else if (pageHeader && isContainerVisible(pageHeader)) {
                    targetContainer = pageHeader;
                    containerType = 'page-header';
                    console.log('📍 使用page-header容器（页面头部模式）');
                } else if (bodyWrap && isContainerVisible(bodyWrap)) {
                    targetContainer = bodyWrap;
                    containerType = 'body-wrap';
                    console.log('📍 使用body-wrap容器（页面包装模式）');
                } else {
                    targetContainer = document.body;
                    containerType = 'body';
                    console.log('📍 使用body容器（兜底模式）');
                }
                
                if (!targetContainer) {
                    console.error('❌ 无法找到合适的容器，跳过时钟初始化');
                    return;
                }

                // 创建时钟DOM
                const bannerClock = document.createElement('div');
                bannerClock.id = 'banner-clock';
                bannerClock.setAttribute('data-container', containerType);
                bannerClock.innerHTML = `
                    <div class="banner-clock-time">加载中...</div>
                    <div class="banner-clock-date">加载中...</div>
                    <div class="banner-clock-weather">
                        <div class="banner-clock-weather-item">
                            <i class="fas fa-sun"></i>
                            <span>晴 25°</span>
                        </div>
                        <div class="banner-clock-weather-item">
                            <i class="fas fa-tint"></i>
                            <span>65%</span>
                        </div>
                    </div>
                    <div class="banner-clock-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>${containerType}模式</span>
                    </div>
                `;
                
                targetContainer.appendChild(bannerClock);
                console.log(`📍 横幅时钟DOM已创建并添加到${containerType}容器`);

                // 模拟组件初始化
                setTimeout(() => {
                    window.bannerClockInstance = {
                        destroy: () => console.log('🗑️ 模拟组件销毁'),
                        containerType: containerType
                    };
                    console.log('🚀 横幅时钟组件已初始化');
                    
                    // 模拟时间更新
                    setInterval(() => {
                        const timeEl = bannerClock.querySelector('.banner-clock-time');
                        const dateEl = bannerClock.querySelector('.banner-clock-date');
                        if (timeEl) {
                            timeEl.textContent = new Date().toLocaleTimeString();
                        }
                        if (dateEl) {
                            const now = new Date();
                            const month = now.getMonth() + 1;
                            const day = now.getDate();
                            const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
                            const weekday = weekdays[now.getDay()];
                            dateEl.textContent = `${month}月${day}日${weekday}`;
                        }
                    }, 1000);
                }, 300);
            }, 100);
        }
        
        // 页面切换函数
        function switchToHomePage() {
            document.body.className = 'page-mode-home';
            updateActiveButton('btn-home');
            console.log('🏠 切换到首页模式');
            setTimeout(() => {
                // 模拟PJAX导航
                document.dispatchEvent(new Event('pjax:start'));
                setTimeout(() => {
                    document.dispatchEvent(new Event('pjax:complete'));
                }, 100);
            }, 50);
        }
        
        function switchToArticlePage() {
            document.body.className = 'page-mode-article';
            updateActiveButton('btn-article');
            console.log('📄 切换到文章页模式');
            setTimeout(() => {
                // 模拟PJAX导航
                document.dispatchEvent(new Event('pjax:start'));
                setTimeout(() => {
                    document.dispatchEvent(new Event('pjax:complete'));
                }, 100);
            }, 50);
        }
        
        function switchToToolPage() {
            document.body.className = 'page-mode-tool';
            updateActiveButton('btn-tool');
            console.log('🔧 切换到工具页模式');
            setTimeout(() => {
                // 模拟PJAX导航
                document.dispatchEvent(new Event('pjax:start'));
                setTimeout(() => {
                    document.dispatchEvent(new Event('pjax:complete'));
                }, 100);
            }, 50);
        }
        
        function updateActiveButton(activeId) {
            document.querySelectorAll('.control-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById(activeId).classList.add('active');
        }
        
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            console.log('🎨 主题切换到:', newTheme);
        }
        
        function forceReinit() {
            console.log('🔧 强制重新初始化时钟...');
            initBannerClock();
        }
        
        // 状态更新
        function updateStatus() {
            const clock = document.getElementById('banner-clock');
            const pageMode = document.body.className;
            
            let status = '';
            status += `页面模式: ${pageMode}\n`;
            status += `时钟存在: ${clock ? '✅' : '❌'}\n`;
            
            if (clock) {
                const containerType = clock.getAttribute('data-container');
                const parent = clock.parentElement;
                status += `容器类型: ${containerType}\n`;
                status += `父容器: ${parent.tagName}${parent.id ? '#' + parent.id : ''}\n`;
                status += `位置: 固定在左下角\n`;
            }
            
            status += `实例存在: ${window.bannerClockInstance ? '✅' : '❌'}\n`;
            status += `时间: ${new Date().toLocaleTimeString()}`;
            
            document.getElementById('status-info').textContent = status;
        }
        
        // PJAX事件监听
        document.addEventListener('pjax:start', function() {
            console.log('🔄 PJAX导航开始，清理旧组件...');
            
            if (window.bannerClockInstance) {
                if (window.bannerClockInstance.destroy) {
                    window.bannerClockInstance.destroy();
                }
                window.bannerClockInstance = null;
                console.log('🗑️ 横幅时钟实例已清理');
            }

            const oldBannerClock = document.getElementById('banner-clock');
            if (oldBannerClock) {
                oldBannerClock.remove();
                console.log('🗑️ 旧的横幅时钟DOM已移除');
            }
        });

        document.addEventListener('pjax:complete', function() {
            console.log('✅ PJAX导航完成，重新初始化时钟系统...');
            
            setTimeout(() => {
                initBannerClock();
            }, 200);
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 页面加载完成，开始测试');
            initBannerClock();
            
            // 定期更新状态
            setInterval(updateStatus, 1000);
        });
    </script>
</body>
</html>
