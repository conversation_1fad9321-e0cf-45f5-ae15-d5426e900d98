<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>横幅时钟显示逻辑优化测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="source/css/banner-clock.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        
        .test-btn {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .test-btn:hover {
            background: #0056b3;
        }
        
        .test-btn.danger {
            background: #dc3545;
        }
        
        .test-btn.danger:hover {
            background: #c82333;
        }
        
        .test-btn.success {
            background: #28a745;
        }
        
        .test-btn.success:hover {
            background: #218838;
        }
        
        .status-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        /* 模拟banners容器 */
        #banners {
            width: 100%;
            height: 300px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 10px;
            position: relative;
            margin: 20px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
        }
        
        .banners-hidden {
            display: none !important;
        }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #333;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 10px 15px;
            cursor: pointer;
            z-index: 10000;
        }
        
        [data-theme="dark"] body {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }
        
        [data-theme="dark"] .test-container {
            background: #2c3e50;
            color: #ecf0f1;
        }
        
        [data-theme="dark"] .test-section {
            background: #34495e;
            border-color: #4a5f7a;
            color: #ecf0f1;
        }
        
        [data-theme="dark"] .status-info {
            background: #34495e;
            color: #ecf0f1;
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">
        <i class="fas fa-moon"></i> 切换主题
    </button>

    <div class="test-container">
        <h1>🕐 横幅时钟显示逻辑优化测试</h1>
        <p>测试横幅时钟组件在不同页面状态下的显示逻辑</p>
        
        <div class="test-section">
            <h3>🎯 测试场景</h3>
            <p><strong>问题描述：</strong>横幅时钟组件只有在进入网页和点击网页刷新时才会出现，返回首页时横幅容器内时钟组件不会出现。</p>
            <p><strong>解决方案：</strong>优化显示逻辑，使时钟组件能够在所有页面显示，不仅仅依赖于banners容器。</p>
        </div>
        
        <div class="test-section">
            <h3>🧪 测试操作</h3>
            <div class="test-buttons">
                <button class="test-btn" onclick="simulateHomePage()">模拟首页（有banners）</button>
                <button class="test-btn" onclick="simulateOtherPage()">模拟其他页面（无banners）</button>
                <button class="test-btn" onclick="simulatePjaxNavigation()">模拟PJAX导航</button>
                <button class="test-btn danger" onclick="removeClock()">移除时钟</button>
                <button class="test-btn success" onclick="initClock()">重新初始化时钟</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 状态信息</h3>
            <div class="status-info" id="status-info">
                等待测试操作...
            </div>
        </div>
    </div>

    <!-- 模拟banners容器 -->
    <div id="banners">
        模拟首页横幅容器 (banners)
    </div>

    <script>
        // 模拟配置中的初始化函数
        function initBannerClock() {
            console.log('🎯 开始初始化横幅时钟...');
            
            // 清理可能存在的旧时钟实例
            if (window.bannerClockInstance) {
                if (window.bannerClockInstance.destroy) {
                    window.bannerClockInstance.destroy();
                }
                window.bannerClockInstance = null;
                console.log('🗑️ 清理旧的横幅时钟实例');
            }

            // 移除可能存在的旧DOM元素
            const existingClock = document.getElementById('banner-clock');
            if (existingClock) {
                existingClock.remove();
                console.log('🗑️ 移除旧的横幅时钟DOM');
            }

            // 优先尝试添加到banners容器（首页）
            let targetContainer = document.getElementById('banners');
            let containerType = 'banners';
            
            // 如果banners容器不存在，则添加到body（其他页面）
            if (!targetContainer) {
                targetContainer = document.body;
                containerType = 'body';
                console.log('📍 banners容器未找到，将时钟添加到body');
            } else {
                console.log('📍 找到banners容器，将时钟添加到banners');
            }

            // 创建新的横幅时钟DOM
            const bannerClock = document.createElement('div');
            bannerClock.id = 'banner-clock';
            bannerClock.setAttribute('data-container', containerType);
            bannerClock.innerHTML = `
                <div class="banner-clock-time">加载中...</div>
                <div class="banner-clock-date">加载中...</div>
                <div class="banner-clock-weather">
                    <div class="banner-clock-weather-item">
                        <i class="fas fa-cloud-sun"></i>
                        <span>获取天气中...</span>
                    </div>
                    <div class="banner-clock-weather-item">
                        <i class="fas fa-tint"></i>
                        <span>--</span>
                    </div>
                </div>
                <div class="banner-clock-location">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>获取位置中...</span>
                </div>
            `;
            
            targetContainer.appendChild(bannerClock);
            console.log(`📍 横幅时钟DOM已创建并添加到${containerType}容器`);

            // 初始化横幅时钟组件（独立）
            setTimeout(() => {
                if (typeof BannerClockWidget !== 'undefined') {
                    window.bannerClockInstance = new BannerClockWidget();
                    console.log('🚀 横幅时钟组件已独立初始化');
                } else {
                    console.warn('⚠️ BannerClockWidget类未找到');
                }
                updateStatus();
            }, 500);
        }

        // 测试函数
        function simulateHomePage() {
            const banners = document.getElementById('banners');
            banners.classList.remove('banners-hidden');
            console.log('🏠 模拟首页状态 - banners容器可见');
            initBannerClock();
        }

        function simulateOtherPage() {
            const banners = document.getElementById('banners');
            banners.classList.add('banners-hidden');
            console.log('📄 模拟其他页面状态 - banners容器隐藏');
            initBannerClock();
        }

        function simulatePjaxNavigation() {
            console.log('🔄 模拟PJAX导航...');

            // 模拟pjax:start事件
            console.log('🔄 触发pjax:start事件');
            document.dispatchEvent(new Event('pjax:start'));

            // 延迟模拟页面切换
            setTimeout(() => {
                // 随机切换页面状态
                const isHomePage = Math.random() > 0.5;
                console.log(`🎯 导航到: ${isHomePage ? '首页' : '其他页面'}`);

                if (isHomePage) {
                    const banners = document.getElementById('banners');
                    banners.classList.remove('banners-hidden');
                } else {
                    const banners = document.getElementById('banners');
                    banners.classList.add('banners-hidden');
                }

                // 模拟pjax:complete事件
                setTimeout(() => {
                    console.log('✅ 触发pjax:complete事件');
                    document.dispatchEvent(new Event('pjax:complete'));
                }, 100);
            }, 50);
        }

        function removeClock() {
            if (window.bannerClockInstance) {
                window.bannerClockInstance.destroy();
                window.bannerClockInstance = null;
            }
            const existingClock = document.getElementById('banner-clock');
            if (existingClock) {
                existingClock.remove();
            }
            console.log('🗑️ 时钟已移除');
            updateStatus();
        }

        function initClock() {
            initBannerClock();
        }

        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            console.log('🎨 主题切换到:', newTheme);
        }

        function updateStatus() {
            const statusEl = document.getElementById('status-info');
            const clock = document.getElementById('banner-clock');
            const banners = document.getElementById('banners');
            
            let status = '';
            status += `时钟存在: ${clock ? '✅' : '❌'}\n`;
            status += `banners容器: ${banners && !banners.classList.contains('banners-hidden') ? '✅ 可见' : '❌ 隐藏'}\n`;
            
            if (clock) {
                const containerType = clock.getAttribute('data-container');
                const parent = clock.parentElement;
                status += `容器类型: ${containerType}\n`;
                status += `父容器: ${parent ? parent.tagName + (parent.id ? '#' + parent.id : '') : '无'}\n`;
                status += `位置: ${clock.style.position || getComputedStyle(clock).position}\n`;
            }
            
            status += `实例存在: ${window.bannerClockInstance ? '✅' : '❌'}\n`;
            status += `当前时间: ${new Date().toLocaleTimeString()}`;
            
            statusEl.textContent = status;
        }

        // 模拟PJAX事件处理
        document.addEventListener('pjax:start', function() {
            console.log('🔄 PJAX导航开始，清理旧组件...');

            // 清理横幅时钟实例
            if (window.bannerClockInstance) {
                if (window.bannerClockInstance.destroy) {
                    window.bannerClockInstance.destroy();
                }
                window.bannerClockInstance = null;
                console.log('🗑️ 横幅时钟实例已清理');
            }

            // 移除旧的横幅时钟DOM元素
            const oldBannerClock = document.getElementById('banner-clock');
            if (oldBannerClock) {
                oldBannerClock.remove();
                console.log('🗑️ 旧的横幅时钟DOM已移除');
            }
        });

        document.addEventListener('pjax:complete', function() {
            console.log('✅ PJAX导航完成，重新初始化时钟系统...');
            console.log('🔍 当前页面URL:', window.location.href);
            console.log('🔍 banners容器存在:', !!document.getElementById('banners'));

            // 延迟初始化，确保新页面DOM完全加载
            setTimeout(() => {
                console.log('⏰ 开始延迟初始化时钟系统...');
                initBannerClock();
            }, 200);

            // 额外的保险措施
            setTimeout(() => {
                const clock = document.getElementById('banner-clock');
                if (!clock) {
                    console.warn('⚠️ 时钟未找到，执行保险初始化...');
                    initBannerClock();
                } else {
                    console.log('✅ 时钟存在，无需保险初始化');
                }
            }, 1000);
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 页面加载完成，开始测试');
            initBannerClock();

            // 定期更新状态
            setInterval(updateStatus, 1000);
        });
    </script>
    
    <script src="source/js/banner-clock.js"></script>
</body>
</html>
