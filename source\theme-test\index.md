---
title: 自动主题切换测试页面
date: 2024-01-20 12:00:00
type: page
---

# 自动主题切换功能测试

这个页面用于测试自动主题切换功能是否正常工作。

## 功能说明

### 🕐 自动切换时间
- **浅色模式**: 早上6点 - 晚上6点
- **深色模式**: 晚上6点 - 早上6点

### 👤 手动切换
- 点击右侧导航栏的主题切换按钮
- 手动切换后2分钟内保持手动设置
- 2分钟后恢复自动切换

### 🎨 视觉反馈
- 切换时会显示通知
- 左下角有状态指示器
- 点击状态指示器查看详情

## 测试方法

1. **查看当前时间和主题**
   - 当前时间: <span id="current-time"></span>
   - 当前主题: <span id="current-theme"></span>

2. **手动切换测试**
   - 点击主题切换按钮
   - 观察通知和状态变化

3. **状态查看**
   - 点击左下角的状态指示器
   - 查看详细信息弹窗

## 开发者工具

<div style="margin: 20px 0; padding: 15px; border: 1px solid var(--efu-border-color); border-radius: 8px;">
  <h3>控制面板</h3>
  <button onclick="testToggleTheme()" style="margin: 5px; padding: 8px 16px;">手动切换主题</button>
  <button onclick="testShowStatus()" style="margin: 5px; padding: 8px 16px;">显示状态</button>
  <button onclick="testForceAutoSwitch()" style="margin: 5px; padding: 8px 16px;">强制自动切换</button>
  <button onclick="testResetManual()" style="margin: 5px; padding: 8px 16px;">重置手动标记</button>
</div>

<div id="test-output" style="margin: 20px 0; padding: 15px; background: var(--efu-card-bg); border-radius: 8px; font-family: monospace; font-size: 14px; max-height: 300px; overflow-y: auto;"></div>

<script>
// 测试脚本
function updateTimeAndTheme() {
  const now = new Date();
  const timeStr = now.toLocaleTimeString('zh-CN');
  const theme = document.documentElement.getAttribute('data-theme') || 'light';
  
  document.getElementById('current-time').textContent = timeStr;
  document.getElementById('current-theme').textContent = theme === 'dark' ? '深色模式' : '浅色模式';
}

function logToOutput(message) {
  const output = document.getElementById('test-output');
  const time = new Date().toLocaleTimeString('zh-CN');
  output.innerHTML += `[${time}] ${message}<br>`;
  output.scrollTop = output.scrollHeight;
}

function testToggleTheme() {
  logToOutput('🎨 执行手动切换主题...');
  if (window.autoThemeSwitcher) {
    window.autoThemeSwitcher.toggleTheme();
    logToOutput('✅ 手动切换完成');
  } else {
    logToOutput('❌ 自动主题切换器未找到');
  }
}

function testShowStatus() {
  logToOutput('📊 获取状态信息...');
  if (window.autoThemeSwitcher) {
    const status = window.autoThemeSwitcher.getStatus();
    logToOutput(`当前主题: ${status.currentTheme}`);
    logToOutput(`当前时间: ${status.currentHour}时`);
    logToOutput(`手动模式: ${status.isManualOverride ? '是' : '否'}`);
    logToOutput(`自动切换启用: ${status.enabled ? '是' : '否'}`);
    const nextSwitch = status.nextSwitchTime || window.autoThemeSwitcher.getNextSwitchTime();
    logToOutput(`下次切换: ${nextSwitch.toLocaleTimeString('zh-CN')}`);
  } else {
    logToOutput('❌ 自动主题切换器未找到');
  }
}

function testForceAutoSwitch() {
  logToOutput('⏰ 强制执行自动切换...');
  if (window.autoThemeSwitcher) {
    window.autoThemeSwitcher.autoSwitchThemeByTime();
    logToOutput('✅ 自动切换检查完成');
  } else {
    logToOutput('❌ 自动主题切换器未找到');
  }
}

function testResetManual() {
  logToOutput('🔄 重置手动切换标记...');
  localStorage.removeItem('theme_manual_override');
  localStorage.removeItem('theme_manual_time');
  logToOutput('✅ 手动标记已清除');
  
  if (window.autoThemeSwitcher) {
    window.autoThemeSwitcher.updateStatusIndicator();
  }
}

// 监听主题切换事件
document.addEventListener('themeChanged', (event) => {
  const { theme, isAutoSwitch } = event.detail;
  const switchType = isAutoSwitch ? '自动' : '手动';
  logToOutput(`🎨 主题${switchType}切换到: ${theme === 'dark' ? '深色模式' : '浅色模式'}`);
  updateTimeAndTheme();
});

// 初始化
document.addEventListener('DOMContentLoaded', function() {
  updateTimeAndTheme();
  setInterval(updateTimeAndTheme, 1000);
  
  logToOutput('🚀 测试页面已加载');
  logToOutput('📝 可以使用上方按钮进行功能测试');
  
  // 检查自动主题切换器是否可用
  setTimeout(() => {
    if (window.autoThemeSwitcher) {
      logToOutput('✅ 自动主题切换器已就绪');
    } else {
      logToOutput('❌ 自动主题切换器未找到，请检查配置');
    }
  }, 2000);
});
</script>

## 故障排除

如果功能不正常，请检查：

1. **配置文件**: 确认 `_config.solitude.yml` 中的配置正确
2. **文件加载**: 检查浏览器开发者工具中是否有 JavaScript 错误
3. **本地存储**: 确认浏览器支持 localStorage
4. **时间设置**: 检查系统时间是否正确

## 技术支持

如有问题，请查看：
- [功能文档](/docs/auto-theme-switcher.md)
- 浏览器开发者工具控制台
- 本页面的测试输出
