<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实环境测试 - 横幅时钟</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="source/css/banner-clock.css">
    <style>
        /* 模拟Solitude主题的基本样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        [data-theme="dark"] body {
            background: #1a1a1a;
            color: #e2e8f0;
        }
        
        /* 模拟页面头部 */
        #page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 模拟导航栏 */
        #nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        [data-theme="dark"] #nav {
            background: rgba(26, 26, 26, 0.9);
        }
        
        /* 模拟首页横幅区域 */
        .recent-top-post-group {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        #bannerGroup {
            position: relative;
        }
        
        #banners {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            color: white;
            margin: 20px 0;
        }
        
        .banners-title-big {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .banners-title-small {
            font-size: 1.2rem;
            opacity: 0.8;
        }
        
        /* 模拟主内容区域 */
        #content-inner {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .recent-posts {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        [data-theme="dark"] .recent-posts {
            background: #2d3748;
        }
        
        /* 控制面板 */
        .control-panel {
            position: fixed;
            top: 80px;
            right: 20px;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            z-index: 2000;
            min-width: 200px;
        }
        
        [data-theme="dark"] .control-panel {
            background: #2d3748;
            color: #e2e8f0;
        }
        
        .control-btn {
            display: block;
            width: 100%;
            padding: 8px 12px;
            margin: 5px 0;
            border: 1px solid #007bff;
            background: #007bff;
            color: white;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            text-align: center;
            text-decoration: none;
        }
        
        .control-btn:hover {
            background: #0056b3;
        }
        
        .control-btn.danger {
            background: #dc3545;
            border-color: #dc3545;
        }
        
        .control-btn.danger:hover {
            background: #c82333;
        }
        
        .status-display {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            font-size: 11px;
            font-family: monospace;
        }
        
        [data-theme="dark"] .status-display {
            background: #1a202c;
        }
        
        /* 页面状态指示器 */
        .page-indicator {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #28a745;
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 2000;
        }
        
        .page-indicator.other-page {
            background: #ffc107;
            color: #000;
        }
        
        /* 隐藏banners的样式 */
        .hide-banners #banners {
            display: none;
        }
    </style>
</head>
<body id="body" data-type="solitude">
    <!-- 页面状态指示器 -->
    <div class="page-indicator" id="page-indicator">首页模式</div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
        <h4 style="margin: 0 0 15px 0; font-size: 14px;">🎮 测试控制</h4>
        <button class="control-btn" onclick="simulateHomePage()">模拟首页</button>
        <button class="control-btn" onclick="simulateOtherPage()">模拟其他页面</button>
        <button class="control-btn" onclick="simulatePjaxNavigation()">PJAX导航</button>
        <button class="control-btn" onclick="toggleTheme()">切换主题</button>
        <button class="control-btn danger" onclick="destroyClock()">销毁时钟</button>
        <button class="control-btn" onclick="forceClock()">强制重建</button>
        
        <div class="status-display" id="status-display">
            状态加载中...
        </div>
    </div>
    
    <!-- 模拟导航栏 -->
    <nav id="nav">
        <div>
            <strong>博客标题</strong>
        </div>
        <div>
            <a href="#" onclick="simulateHomePage()">首页</a> |
            <a href="#" onclick="simulateOtherPage()">文章</a> |
            <a href="#" onclick="simulateOtherPage()">分类</a>
        </div>
    </nav>
    
    <!-- 页面头部 -->
    <header id="page-header">
        <div class="recent-top-post-group" id="home_top">
            <div id="bannerGroup">
                <div id="banners">
                    <div class="banners-title-big">欢迎来到我的博客</div>
                    <div class="banners-title-small">分享技术与生活</div>
                </div>
            </div>
        </div>
    </header>
    
    <!-- 主内容区域 -->
    <main id="content-inner">
        <div class="recent-posts">
            <h2>最新文章</h2>
            <p>这里是文章列表内容...</p>
            <p>在真实环境中，这里会显示博客文章。</p>
            <p>横幅时钟应该在左下角显示，无论是在首页还是其他页面。</p>
        </div>
    </main>

    <!-- 引入实际的配置脚本 -->
    <script>
        // 模拟pjax对象
        window.pjax = {
            loadUrl: function(url) {
                console.log('模拟PJAX加载:', url);
                simulatePjaxNavigation();
            }
        };
        
        // 从配置文件中复制的初始化函数
        function initBannerClock() {
            console.log('🎯 开始初始化横幅时钟...');
            console.log('🔍 当前页面URL:', window.location.href);
            console.log('🔍 当前页面路径:', window.location.pathname);
            
            // 清理可能存在的旧时钟实例
            if (window.bannerClockInstance) {
                if (window.bannerClockInstance.destroy) {
                    window.bannerClockInstance.destroy();
                }
                window.bannerClockInstance = null;
                console.log('🗑️ 清理旧的横幅时钟实例');
            }

            // 移除可能存在的旧DOM元素
            const existingClock = document.getElementById('banner-clock');
            if (existingClock) {
                existingClock.remove();
                console.log('🗑️ 移除旧的横幅时钟DOM');
            }

            // 等待一小段时间，确保DOM完全加载
            setTimeout(() => {
                // 优先尝试添加到banners容器（首页）
                let targetContainer = document.getElementById('banners');
                let containerType = 'banners';
                
                console.log('🔍 DOM检查结果:');
                console.log('- banners容器:', targetContainer ? '✅ 存在' : '❌ 不存在');
                console.log('- body元素:', document.body ? '✅ 存在' : '❌ 不存在');
                
                // 如果banners容器不存在，则添加到body（其他页面）
                if (!targetContainer) {
                    targetContainer = document.body;
                    containerType = 'body';
                    console.log('📍 banners容器未找到，将时钟添加到body');
                } else {
                    console.log('📍 找到banners容器，将时钟添加到banners');
                }
                
                // 确保目标容器存在
                if (!targetContainer) {
                    console.error('❌ 无法找到合适的容器，跳过时钟初始化');
                    return;
                }

                // 创建新的横幅时钟DOM
                const bannerClock = document.createElement('div');
                bannerClock.id = 'banner-clock';
                bannerClock.setAttribute('data-container', containerType);
                bannerClock.innerHTML = `
                    <div class="banner-clock-time">加载中...</div>
                    <div class="banner-clock-date">加载中...</div>
                    <div class="banner-clock-weather">
                        <div class="banner-clock-weather-item">
                            <i class="fas fa-cloud-sun"></i>
                            <span>获取天气中...</span>
                        </div>
                        <div class="banner-clock-weather-item">
                            <i class="fas fa-tint"></i>
                            <span>--</span>
                        </div>
                    </div>
                    <div class="banner-clock-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>获取位置中...</span>
                    </div>
                `;
                
                targetContainer.appendChild(bannerClock);
                console.log(`📍 横幅时钟DOM已创建并添加到${containerType}容器`);

                // 初始化横幅时钟组件（独立）
                setTimeout(() => {
                    if (typeof BannerClockWidget !== 'undefined') {
                        window.bannerClockInstance = new BannerClockWidget();
                        console.log('🚀 横幅时钟组件已独立初始化');
                    } else {
                        // 模拟组件
                        window.bannerClockInstance = {
                            destroy: () => console.log('🗑️ 模拟组件销毁'),
                            containerType: containerType
                        };
                        console.log('🚀 模拟横幅时钟组件已初始化');
                        
                        // 模拟时间更新
                        setInterval(() => {
                            const timeEl = bannerClock.querySelector('.banner-clock-time');
                            const dateEl = bannerClock.querySelector('.banner-clock-date');
                            if (timeEl) {
                                timeEl.textContent = new Date().toLocaleTimeString();
                            }
                            if (dateEl) {
                                const now = new Date();
                                const month = now.getMonth() + 1;
                                const day = now.getDate();
                                const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
                                const weekday = weekdays[now.getDay()];
                                dateEl.textContent = `${month}月${day}日${weekday}`;
                            }
                        }, 1000);
                    }
                }, 300);
            }, 100);
        }
        
        // 测试函数
        function simulateHomePage() {
            document.body.classList.remove('hide-banners');
            document.getElementById('page-indicator').textContent = '首页模式';
            document.getElementById('page-indicator').className = 'page-indicator';
            console.log('🏠 切换到首页模式');
            
            // 模拟PJAX导航
            setTimeout(() => {
                document.dispatchEvent(new Event('pjax:complete'));
            }, 100);
        }
        
        function simulateOtherPage() {
            document.body.classList.add('hide-banners');
            document.getElementById('page-indicator').textContent = '其他页面模式';
            document.getElementById('page-indicator').className = 'page-indicator other-page';
            console.log('📄 切换到其他页面模式');
            
            // 模拟PJAX导航
            setTimeout(() => {
                document.dispatchEvent(new Event('pjax:complete'));
            }, 100);
        }
        
        function simulatePjaxNavigation() {
            console.log('🔄 模拟PJAX导航...');
            
            // 触发pjax:start
            document.dispatchEvent(new Event('pjax:start'));
            
            // 随机选择页面类型
            setTimeout(() => {
                if (Math.random() > 0.5) {
                    simulateHomePage();
                } else {
                    simulateOtherPage();
                }
            }, 200);
        }
        
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            console.log('🎨 主题切换到:', newTheme);
        }
        
        function destroyClock() {
            if (window.bannerClockInstance) {
                window.bannerClockInstance.destroy();
                window.bannerClockInstance = null;
            }
            const clock = document.getElementById('banner-clock');
            if (clock) {
                clock.remove();
            }
            console.log('💥 时钟已销毁');
        }
        
        function forceClock() {
            console.log('🔧 强制重建时钟...');
            destroyClock();
            setTimeout(initBannerClock, 100);
        }
        
        // 状态更新
        function updateStatus() {
            const clock = document.getElementById('banner-clock');
            const banners = document.getElementById('banners');
            const isHomePage = !document.body.classList.contains('hide-banners');
            
            let status = '';
            status += `页面类型: ${isHomePage ? '首页' : '其他页面'}\n`;
            status += `时钟存在: ${clock ? '✅' : '❌'}\n`;
            status += `banners可见: ${banners && !document.body.classList.contains('hide-banners') ? '✅' : '❌'}\n`;
            
            if (clock) {
                const containerType = clock.getAttribute('data-container');
                const parent = clock.parentElement;
                status += `容器类型: ${containerType}\n`;
                status += `父容器: ${parent.tagName}${parent.id ? '#' + parent.id : ''}\n`;
            }
            
            status += `实例存在: ${window.bannerClockInstance ? '✅' : '❌'}\n`;
            status += `时间: ${new Date().toLocaleTimeString()}`;
            
            document.getElementById('status-display').textContent = status;
        }
        
        // PJAX事件监听
        document.addEventListener('pjax:start', function() {
            console.log('🔄 PJAX导航开始，清理旧组件...');
            
            if (window.bannerClockInstance) {
                if (window.bannerClockInstance.destroy) {
                    window.bannerClockInstance.destroy();
                }
                window.bannerClockInstance = null;
                console.log('🗑️ 横幅时钟实例已清理');
            }

            const oldBannerClock = document.getElementById('banner-clock');
            if (oldBannerClock) {
                oldBannerClock.remove();
                console.log('🗑️ 旧的横幅时钟DOM已移除');
            }
        });

        document.addEventListener('pjax:complete', function() {
            console.log('✅ PJAX导航完成，重新初始化时钟系统...');
            
            setTimeout(() => {
                initBannerClock();
            }, 200);
            
            setTimeout(() => {
                const clock = document.getElementById('banner-clock');
                if (!clock) {
                    console.warn('⚠️ 时钟未找到，执行保险初始化...');
                    initBannerClock();
                }
            }, 1000);
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 页面加载完成，开始初始化');
            initBannerClock();
            
            // 定期更新状态
            setInterval(updateStatus, 1000);
        });
    </script>
    
    <!-- 引入实际的时钟组件 -->
    <script src="source/js/banner-clock.js"></script>
</body>
</html>
