// 天气数据管理器 - 统一管理所有时钟组件的天气数据请求
class WeatherManager {
  constructor() {
    this.apiKey = 'f7105a35d70abd283a0dd10f87cf2516';
    this.city = null;
    this.weatherData = null;
    this.locationObtained = false;
    this.storageKey = 'shared-weather-data';
    this.cityStorageKey = 'shared-city-data';
    this.lastUpdateTime = null;
    this.updateInterval = 30 * 60 * 1000; // 30分钟基础间隔
    this.subscribers = new Set(); // 订阅者列表
    this.isInitialized = false;
    this.apiCallCount = 0; // 每日API调用计数
    this.monthlyCallCount = 0; // 每月API调用计数
    this.dailyLimit = 150; // 每日限制
    this.monthlyLimit = 4500; // 每月安全限制
    this.apiStatsKey = 'weather-api-stats';
    this.monthlyStatsKey = 'weather-monthly-stats';
    this.locationMethod = 'unknown'; // 记录位置获取方式
    this.locationAttempts = 0; // 位置获取尝试次数

    console.log('🌤️ 天气数据管理器初始化...');
    this.loadApiStats();
    this.initLocationSelector();
  }

  // 初始化方法
  async init() {
    if (this.isInitialized) {
      console.log('⚠️ 天气管理器已初始化');
      return;
    }

    console.log('🚀 天气数据管理器开始初始化...');

    // 先尝试加载保存的数据
    this.loadSavedData();

    // 如果有保存的城市和有效的天气数据，先使用缓存数据
    if (this.city && this.weatherData && !this.isDataExpired()) {
      console.log('📦 使用缓存的天气数据');
      this.notifySubscribers();
    }

    // 启动定时更新
    this.startPeriodicUpdate();

    this.isInitialized = true;
    console.log('✅ 天气数据管理器初始化完成');

    // 后台异步获取位置和天气数据
    this.backgroundLocationUpdate();
  }

  // 后台位置更新（基于浏览器地理位置）
  async backgroundLocationUpdate() {
    console.log('🔄 开始位置获取...');
    console.log('📊 当前状态详情:');
    console.log('  - 城市:', this.city);
    console.log('  - 位置获取状态:', this.locationObtained);
    console.log('  - 位置获取方式:', this.locationMethod);
    console.log('  - 尝试次数:', this.locationAttempts);
    console.log('  - 当前环境:', location.protocol, location.hostname);

    // 检查是否需要位置更新
    const needLocationUpdate = this.shouldUpdateLocation();
    console.log('📍 是否需要位置更新:', needLocationUpdate);

    if (needLocationUpdate) {
      console.log('📍 启动位置获取流程...');

      // 延迟一段时间，确保页面完全加载
      console.log('⏳ 等待2秒确保页面完全加载...');
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 第一步：申请浏览器地理位置权限并获取位置
      console.log('📱 开始申请浏览器地理位置权限...');
      const browserLocation = await this.requestBrowserLocation();

      if (browserLocation) {
        this.city = browserLocation;
        this.locationMethod = 'browser';
        this.locationObtained = true;
        console.log('✅ 浏览器定位成功:', this.city);
        this.saveData();
        await this.getWeatherData();
        return;
      } else {
        // 第二步：如果浏览器定位失败，使用默认城市
        console.log('🏙️ 浏览器定位失败，使用默认城市');
        this.setDefaultCity();
        return;
      }
    } else {
      console.log('⏭️ 跳过位置获取，使用现有数据');
    }

    // 如果有城市但没有天气数据，或数据过期，获取天气
    if (this.city && (!this.weatherData || this.isDataExpired())) {
      console.log('🌤️ 需要更新天气数据...');
      await this.getWeatherData();
    }

    console.log('✅ 位置更新完成');
  }

  // 用户主动触发的位置获取
  async triggerLocationRequest() {
    console.log('👆 用户主动触发位置获取');

    const browserLocation = await this.requestBrowserLocation();

    if (browserLocation) {
      this.city = browserLocation;
      this.locationMethod = 'browser';
      this.locationObtained = true;
      console.log('✅ 用户触发定位成功:', this.city);
      this.saveData();
      await this.getWeatherData();
      return browserLocation;
    } else {
      console.log('❌ 用户触发定位失败');
      return null;
    }
  }

  // 强制重新获取位置（清除缓存）
  async forceLocationUpdate() {
    console.log('🔄 强制重新获取位置...');

    // 重置状态
    this.locationAttempts = 0;
    this.locationObtained = false;
    this.locationMethod = 'unknown';

    // 清除缓存的城市信息
    localStorage.removeItem(this.cityStorageKey);
    this.city = null;

    console.log('🗑️ 已清除位置缓存，重新开始位置获取');

    // 重新开始位置获取流程
    await this.backgroundLocationUpdate();
  }

  // 判断是否需要更新位置
  shouldUpdateLocation() {
    // 检查是否在HTTPS环境下
    if (location.protocol !== 'https:' &&
        location.hostname !== 'localhost' &&
        location.hostname !== '127.0.0.1' &&
        !location.hostname.endsWith('.local')) {
      console.warn('⚠️ 地理位置API需要HTTPS环境，当前为:', location.protocol, location.hostname);
      return false;
    }

    // 没有城市时需要更新
    if (!this.city) {
      console.log('📍 无城市信息，需要获取位置');
      return true;
    }

    // 城市是默认的北京时需要更新
    if (this.city === '北京' || this.city === '北京市') {
      console.log('📍 当前为默认城市，尝试获取真实位置');
      return true;
    }

    // 位置获取方式未知或为默认时需要更新
    if (!this.locationMethod || this.locationMethod === 'default' || this.locationMethod === 'unknown') {
      console.log('📍 位置获取方式未知，尝试重新获取');
      return true;
    }

    // 如果从未尝试过浏览器定位，则尝试一次
    if (this.locationMethod !== 'browser' && this.locationAttempts === 0) {
      console.log('📍 从未尝试过浏览器定位，进行尝试');
      return true;
    }

    console.log('📍 位置信息已获取，无需更新');
    return false;
  }

  // 订阅天气数据更新
  subscribe(callback) {
    this.subscribers.add(callback);
    console.log(`📡 新增订阅者，当前订阅者数量: ${this.subscribers.size}`);
    
    // 如果已有数据，立即通知新订阅者
    if (this.weatherData && this.city) {
      callback({
        city: this.city,
        weather: this.weatherData,
        timestamp: this.lastUpdateTime
      });
    }
  }

  // 取消订阅
  unsubscribe(callback) {
    this.subscribers.delete(callback);
    console.log(`📡 移除订阅者，当前订阅者数量: ${this.subscribers.size}`);
  }

  // onUpdate方法（向后兼容）
  onUpdate(callback) {
    return this.subscribe(callback);
  }

  // 通知所有订阅者
  notifySubscribers() {
    const data = {
      city: this.city,
      weather: this.weatherData,
      timestamp: this.lastUpdateTime
    };
    
    console.log(`📢 通知 ${this.subscribers.size} 个订阅者天气数据更新`);
    this.subscribers.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('❌ 通知订阅者失败:', error);
      }
    });
  }

  // 从localStorage加载保存的数据
  loadSavedData() {
    try {
      // 加载城市
      const savedCity = localStorage.getItem(this.cityStorageKey);
      if (savedCity) {
        this.city = savedCity;
        console.log('🏙️ 加载保存的城市:', this.city);
      }

      // 加载天气数据
      const savedData = localStorage.getItem(this.storageKey);
      if (savedData) {
        const data = JSON.parse(savedData);
        this.weatherData = data.weather;
        this.lastUpdateTime = data.timestamp;
        console.log('🌤️ 加载保存的天气数据:', this.weatherData);
      }
    } catch (error) {
      console.warn('⚠️ 加载保存的数据失败:', error);
    }
  }

  // 保存数据到localStorage
  saveData() {
    try {
      // 保存城市
      if (this.city) {
        localStorage.setItem(this.cityStorageKey, this.city);
      }

      // 保存天气数据
      if (this.weatherData) {
        const data = {
          weather: this.weatherData,
          timestamp: this.lastUpdateTime
        };
        localStorage.setItem(this.storageKey, JSON.stringify(data));
      }
      console.log('💾 天气数据已保存');
    } catch (error) {
      console.warn('⚠️ 保存数据失败:', error);
    }
  }

  // 检查数据是否过期（更保守的缓存策略）
  isDataExpired() {
    if (!this.lastUpdateTime) return true;
    const now = Date.now();

    // 天气数据缓存2小时，减少API调用
    const cacheTime = 2 * 60 * 60 * 1000; // 2小时
    return (now - this.lastUpdateTime) > cacheTime;
  }

  // 获取当前位置（带重试机制）
  async getCurrentLocation() {
    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        console.warn('⚠️ 浏览器不支持地理位置');
        this.setDefaultCity();
        resolve();
        return;
      }

      let retryCount = 0;
      const maxRetries = 3;

      const tryGetLocation = () => {
        retryCount++;
        console.log(`📍 正在获取位置... (尝试 ${retryCount}/${maxRetries})`);

        // 移动端使用更长的超时时间
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const timeout = isMobile ? 20000 : 15000; // 移动端20秒，桌面端15秒

        navigator.geolocation.getCurrentPosition(
          async (position) => {
            console.log('✅ 位置获取成功:', position.coords);
            await this.reverseGeocode(position.coords.latitude, position.coords.longitude);
            resolve();
          },
          (error) => {
            console.warn(`⚠️ 位置获取失败 (尝试 ${retryCount}/${maxRetries}):`, error.message);

            if (retryCount < maxRetries) {
              // 延迟后重试
              const delay = retryCount * 2000; // 递增延迟：2秒、4秒
              console.log(`🔄 ${delay/1000}秒后重试位置获取...`);
              setTimeout(tryGetLocation, delay);
            } else {
              console.warn('❌ 位置获取最终失败，使用默认城市');
              this.setDefaultCity();
              resolve();
            }
          },
          {
            timeout: timeout,
            enableHighAccuracy: false,
            maximumAge: 600000 // 10分钟缓存，移动端网络较慢
          }
        );
      };

      // 延迟启动位置获取，等待页面稳定
      setTimeout(tryGetLocation, 1000);
    });
  }

  // 申请浏览器地理位置权限并获取位置
  async requestBrowserLocation() {
    return new Promise((resolve) => {
      // 检查浏览器是否支持地理位置
      if (!navigator.geolocation) {
        console.warn('⚠️ 浏览器不支持地理位置功能');
        resolve(null);
        return;
      }

      // 检查HTTPS环境
      if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
        console.warn('⚠️ 地理位置API需要HTTPS环境，当前协议:', location.protocol);
        resolve(null);
        return;
      }

      console.log('📱 申请浏览器地理位置权限...');
      console.log('🌐 当前环境:', location.protocol, location.hostname);
      this.locationAttempts++;

      // 设置超时处理机制
      const timeoutDuration = 15000; // 15秒超时，给用户更多时间
      const startTime = Date.now();

      let timeoutId = setTimeout(() => {
        console.warn('⏰ 浏览器定位超时（15秒）');
        resolve(null);
      }, timeoutDuration);

      // 申请地理位置权限
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          clearTimeout(timeoutId);
          const elapsed = Date.now() - startTime;
          console.log(`✅ 浏览器定位成功 (耗时: ${elapsed}ms):`, position.coords);

          try {
            // 通过坐标获取城市名称
            const city = await this.reverseGeocode(position.coords.latitude, position.coords.longitude);
            if (city) {
              console.log('✅ 坐标转换城市成功:', city);
              resolve(city);
            } else {
              console.warn('⚠️ 坐标转换城市失败');
              resolve(null);
            }
          } catch (error) {
            console.warn('⚠️ 坐标转换异常:', error);
            resolve(null);
          }
        },
        (error) => {
          clearTimeout(timeoutId);
          const elapsed = Date.now() - startTime;
          console.warn(`⚠️ 浏览器定位失败 (耗时: ${elapsed}ms):`, error.message);

          // 分析失败原因并提供详细信息
          switch (error.code) {
            case error.PERMISSION_DENIED:
              console.warn('🚫 用户拒绝了地理位置权限');
              console.log('💡 提示：用户可以在浏览器地址栏左侧点击锁图标重新允许位置权限');
              break;
            case error.POSITION_UNAVAILABLE:
              console.warn('📍 位置信息不可用');
              console.log('💡 提示：可能是GPS信号弱或网络问题');
              break;
            case error.TIMEOUT:
              console.warn('⏰ 地理位置请求超时');
              console.log('💡 提示：定位超时，可能是网络较慢');
              break;
            default:
              console.warn('❌ 未知的地理位置错误');
              break;
          }

          resolve(null);
        },
        {
          timeout: timeoutDuration,
          enableHighAccuracy: false, // 改为false，提高成功率
          maximumAge: 600000 // 10分钟内的缓存位置可接受
        }
      );
    });
  }

  // 手动触发位置获取（用于用户主动点击）
  async requestLocationWithUserInteraction() {
    console.log('👆 用户主动触发位置获取');
    return await this.requestBrowserLocation();
  }



  // 设置默认城市（智能推断）
  setDefaultCity() {
    // 尝试从时区推断城市
    try {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      console.log('🌍 检测到时区:', timezone);

      // 时区到城市的映射
      const timezoneToCity = {
        'Asia/Shanghai': '上海',
        'Asia/Beijing': '北京',
        'Asia/Chongqing': '重庆',
        'Asia/Urumqi': '乌鲁木齐',
        'Asia/Hong_Kong': '香港',
        'Asia/Macau': '澳门',
        'Asia/Taipei': '台北',
        'Asia/Tokyo': '东京',
        'Asia/Seoul': '首尔',
        'Asia/Singapore': '新加坡',
        'America/New_York': '纽约',
        'America/Los_Angeles': '洛杉矶',
        'Europe/London': '伦敦',
        'Europe/Paris': '巴黎'
      };

      this.city = timezoneToCity[timezone] || '北京';
      this.locationMethod = timezone ? 'timezone' : 'default';
      console.log('🏙️ 使用智能推断城市:', this.city, '(方法:', this.locationMethod, ')');
    } catch (error) {
      console.warn('⚠️ 时区检测失败:', error);
      this.city = '北京';
      this.locationMethod = 'default';
      console.log('🏙️ 使用默认城市:', this.city);
    }

    this.locationObtained = true;
    this.saveData();
    this.getWeatherData();
  }

  // 逆地理编码（坐标转城市名称）
  async reverseGeocode(latitude, longitude) {
    try {
      console.log('🔄 正在进行逆地理编码...');

      // 优先使用高德地图逆地理编码API（与天气API同源）
      try {
        console.log('🌐 使用高德地图逆地理编码');

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 8000);

        const amapResponse = await fetch(
          `https://restapi.amap.com/v3/geocode/regeo?key=${this.apiKey}&location=${longitude},${latitude}&output=json&radius=1000&extensions=base`,
          {
            signal: controller.signal,
            headers: {
              'Accept': 'application/json'
            }
          }
        );

        clearTimeout(timeoutId);

        if (amapResponse.ok) {
          const amapData = await amapResponse.json();
          console.log('🌍 高德逆地理编码响应:', amapData);

          if (amapData.status === '1' && amapData.regeocode && amapData.regeocode.addressComponent) {
            const addressComponent = amapData.regeocode.addressComponent;
            let city = addressComponent.city || addressComponent.district || addressComponent.province;

            if (city && city !== '[]') {
              // 标准化城市名称
              if (!city.endsWith('市') && !city.endsWith('县') && !city.endsWith('区')) {
                city += '市';
              }

              console.log('✅ 高德逆地理编码成功:', city);
              return city;
            }
          }
        }
      } catch (error) {
        console.warn('⚠️ 高德逆地理编码失败:', error.message);
      }

      // 备用逆地理编码服务
      const services = [
        {
          name: 'BigDataCloud',
          url: `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${latitude}&longitude=${longitude}&localityLanguage=zh`,
          parser: (data) => data.city || data.locality || data.principalSubdivision
        },
        {
          name: 'OpenStreetMap',
          url: `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&accept-language=zh-CN`,
          headers: { 'User-Agent': 'WeatherManager/1.0' },
          parser: (data) => data.address ? (data.address.city || data.address.town || data.address.county) : null
        }
      ];

      for (const service of services) {
        try {
          console.log(`🌐 尝试 ${service.name} 逆地理编码`);

          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 6000);

          const response = await fetch(service.url, {
            headers: service.headers || {},
            signal: controller.signal
          });

          clearTimeout(timeoutId);

          if (response.ok) {
            const data = await response.json();
            console.log(`🌍 ${service.name} 响应:`, data);

            const city = service.parser(data);
            if (city && city !== 'Unknown') {
              // 标准化城市名称
              let normalizedCity = city;
              if (!normalizedCity.endsWith('市') && !normalizedCity.endsWith('县') && !normalizedCity.endsWith('区')) {
                normalizedCity += '市';
              }

              console.log(`✅ ${service.name} 逆地理编码成功:`, normalizedCity);
              return normalizedCity;
            }

            console.warn(`⚠️ 无法从 ${service.name} 结果中提取城市信息`);
          } else {
            console.warn(`⚠️ ${service.name} 逆地理编码请求失败:`, response.status);
          }
        } catch (error) {
          console.warn(`⚠️ ${service.name} 逆地理编码失败:`, error.message);
        }
      }

      console.warn('❌ 所有逆地理编码服务均失败');
      return null;
    } catch (error) {
      console.warn('❌ 逆地理编码异常:', error);
      return null;
    }
  }

  // 获取天气数据
  async getWeatherData() {
    if (!this.city) {
      console.warn('⚠️ 城市信息未设置，无法获取天气');
      return;
    }

    // 检查是否可以进行API调用
    if (!this.canMakeApiCall()) {
      console.warn('⚠️ 已达到API调用限制，使用缓存数据');
      return;
    }

    try {
      console.log('🌤️ 正在获取天气数据:', this.city);

      const response = await fetch(
        `https://restapi.amap.com/v3/weather/weatherInfo?key=${this.apiKey}&city=${encodeURIComponent(this.city)}&extensions=base`
      );

      // 记录API调用
      this.recordApiCall();

      if (response.ok) {
        const data = await response.json();
        console.log('🌤️ 天气API响应:', data);

        if (data.status === '1' && data.lives && data.lives.length > 0) {
          this.weatherData = data.lives[0];
          this.lastUpdateTime = Date.now();
          this.saveData();
          this.notifySubscribers();
          console.log('✅ 天气数据获取成功');
        } else {
          console.warn('⚠️ 天气API返回错误:', data.info || '未知错误');
          this.setDefaultWeather();
        }
      } else {
        console.warn('⚠️ 天气请求失败:', response.status);
        this.setDefaultWeather();
      }
    } catch (error) {
      console.warn('⚠️ 获取天气数据出错:', error);
      this.setDefaultWeather();
    }
  }

  // 设置默认天气
  setDefaultWeather() {
    this.weatherData = {
      weather: '多云',
      temperature: '25',
      humidity: '65'
    };
    this.lastUpdateTime = Date.now();
    this.saveData();
    this.notifySubscribers();
    console.log('🌤️ 使用默认天气数据');
  }

  // 获取智能更新间隔（针对5000次/月限制优化）
  getSmartUpdateInterval() {
    const now = new Date();
    const hour = now.getHours();

    // 检查当前API使用情况，如果接近限制则延长间隔
    const usagePercentage = this.apiCallCount / this.dailyLimit;

    let baseInterval;
    // 活跃时段（9:00-18:00）：30分钟
    if (hour >= 9 && hour < 18) {
      baseInterval = 30 * 60 * 1000;
    }
    // 一般时段（7:00-9:00, 18:00-22:00）：60分钟
    else if ((hour >= 7 && hour < 9) || (hour >= 18 && hour < 22)) {
      baseInterval = 60 * 60 * 1000;
    }
    // 休息时段（22:00-7:00）：120分钟
    else {
      baseInterval = 120 * 60 * 1000;
    }

    // 根据使用率动态调整间隔
    if (usagePercentage > 0.8) {
      baseInterval *= 2; // 使用率超过80%时，间隔翻倍
    } else if (usagePercentage > 0.6) {
      baseInterval *= 1.5; // 使用率超过60%时，间隔增加50%
    }

    return baseInterval;
  }

  // 启动智能定时更新
  startPeriodicUpdate() {
    const scheduleNextUpdate = () => {
      const interval = this.getSmartUpdateInterval();
      const intervalMinutes = interval / 60000;

      setTimeout(() => {
        if (this.city) {
          console.log(`⏰ 智能定时更新天气数据（当前间隔：${intervalMinutes}分钟）`);
          this.getWeatherData();
        }
        scheduleNextUpdate(); // 递归调度下次更新
      }, interval);
    };

    scheduleNextUpdate();
    console.log('⏰ 智能天气数据定时更新已启动');
  }

  // 手动刷新天气数据
  async refresh() {
    console.log('🔄 手动刷新天气数据');
    await this.getWeatherData();
  }

  // 加载API统计数据
  loadApiStats() {
    try {
      // 加载每日统计
      const dailyStats = localStorage.getItem(this.apiStatsKey);
      if (dailyStats) {
        const data = JSON.parse(dailyStats);
        const today = new Date().toDateString();

        // 如果是新的一天，重置每日计数
        if (data.date !== today) {
          this.apiCallCount = 0;
          this.saveApiStats();
        } else {
          this.apiCallCount = data.count || 0;
        }
      }

      // 加载每月统计
      const monthlyStats = localStorage.getItem(this.monthlyStatsKey);
      if (monthlyStats) {
        const data = JSON.parse(monthlyStats);
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();

        // 如果是新的月份，重置月度计数
        if (data.month !== currentMonth || data.year !== currentYear) {
          this.monthlyCallCount = 0;
          this.saveMonthlyStats();
        } else {
          this.monthlyCallCount = data.count || 0;
        }
      }
    } catch (error) {
      console.warn('⚠️ 加载API统计失败:', error);
      this.apiCallCount = 0;
      this.monthlyCallCount = 0;
    }
  }

  // 保存每日API统计数据
  saveApiStats() {
    try {
      const stats = {
        date: new Date().toDateString(),
        count: this.apiCallCount,
        timestamp: Date.now()
      };
      localStorage.setItem(this.apiStatsKey, JSON.stringify(stats));
    } catch (error) {
      console.warn('⚠️ 保存每日API统计失败:', error);
    }
  }

  // 保存每月API统计数据
  saveMonthlyStats() {
    try {
      const now = new Date();
      const stats = {
        month: now.getMonth(),
        year: now.getFullYear(),
        count: this.monthlyCallCount,
        timestamp: Date.now()
      };
      localStorage.setItem(this.monthlyStatsKey, JSON.stringify(stats));
    } catch (error) {
      console.warn('⚠️ 保存每月API统计失败:', error);
    }
  }

  // 记录API调用
  recordApiCall() {
    this.apiCallCount++;
    this.monthlyCallCount++;
    this.saveApiStats();
    this.saveMonthlyStats();

    const dailyPercentage = (this.apiCallCount / this.dailyLimit * 100).toFixed(1);
    const monthlyPercentage = (this.monthlyCallCount / this.monthlyLimit * 100).toFixed(1);

    console.log(`📊 API调用统计:`);
    console.log(`   今日: ${this.apiCallCount}/${this.dailyLimit} (${dailyPercentage}%)`);
    console.log(`   本月: ${this.monthlyCallCount}/${this.monthlyLimit} (${monthlyPercentage}%)`);

    // 如果接近限制，发出警告
    if (this.monthlyCallCount > this.monthlyLimit * 0.9) {
      console.error(`🚨 月度API调用已达到90%: ${this.monthlyCallCount}/${this.monthlyLimit}`);
    } else if (this.monthlyCallCount > this.monthlyLimit * 0.8) {
      console.warn(`⚠️ 月度API调用已达到80%: ${this.monthlyCallCount}/${this.monthlyLimit}`);
    } else if (this.apiCallCount > this.dailyLimit * 0.8) {
      console.warn(`⚠️ 今日API调用已达到80%: ${this.apiCallCount}/${this.dailyLimit}`);
    }
  }

  // 检查是否可以进行API调用
  canMakeApiCall() {
    // 检查月度限制
    if (this.monthlyCallCount >= this.monthlyLimit) {
      console.error('❌ 已达到月度API调用限制，跳过请求');
      return false;
    }

    // 检查每日限制
    if (this.apiCallCount >= this.dailyLimit) {
      console.warn('❌ 已达到今日API调用限制，跳过请求');
      return false;
    }

    return true;
  }

  // 创建位置选择器模态框
  createLocationModal() {
    if (document.getElementById('location-selector-modal')) {
      console.log('📍 位置选择器模态框已存在');
      return;
    }

    const locationModal = document.createElement('div');
    locationModal.id = 'location-selector-modal';
    locationModal.innerHTML = `
      <div class="location-modal-content">
        <div class="location-modal-header">
          <h3>选择城市</h3>
          <button class="location-close-btn">&times;</button>
        </div>
        <div class="location-modal-body">
          <div class="location-search">
            <input type="text" id="city-search-input" placeholder="搜索城市...">
            <div id="city-suggestions" class="city-suggestions"></div>
          </div>
          <div class="popular-cities">
            <h4>热门城市</h4>
            <div class="city-buttons">
              <button class="city-btn" data-city="北京">北京</button>
              <button class="city-btn" data-city="上海">上海</button>
              <button class="city-btn" data-city="广州">广州</button>
              <button class="city-btn" data-city="深圳">深圳</button>
              <button class="city-btn" data-city="杭州">杭州</button>
              <button class="city-btn" data-city="南京">南京</button>
              <button class="city-btn" data-city="成都">成都</button>
              <button class="city-btn" data-city="重庆">重庆</button>
            </div>
          </div>
        </div>
        <div class="location-modal-footer">
          <button id="location-cancel-btn" class="btn-cancel">取消</button>
          <button id="location-confirm-btn" class="btn-confirm" disabled>确认</button>
        </div>
      </div>
    `;
    document.body.appendChild(locationModal);
    console.log('📍 位置选择器模态框已创建');
  }

  // 初始化位置选择器
  initLocationSelector() {
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupLocationSelector());
    } else {
      this.setupLocationSelector();
    }
  }

  // 设置位置选择器事件
  setupLocationSelector() {
    // 确保模态框存在
    if (!document.getElementById('location-selector-modal')) {
      console.warn('⚠️ 位置选择器模态框未找到，尝试创建');
      this.createLocationModal();
    }

    const modal = document.getElementById('location-selector-modal');
    const searchInput = document.getElementById('city-search-input');
    const suggestions = document.getElementById('city-suggestions');
    const confirmBtn = document.getElementById('location-confirm-btn');
    const cancelBtn = document.getElementById('location-cancel-btn');
    const closeBtn = document.querySelector('.location-close-btn');
    const cityBtns = document.querySelectorAll('.city-btn');

    if (!modal) {
      console.error('❌ 位置选择器模态框创建失败');
      return;
    }

    console.log('✅ 位置选择器模态框已找到，设置事件监听器');

    let selectedCity = '';

    // 热门城市选择
    cityBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        cityBtns.forEach(b => b.classList.remove('selected'));
        btn.classList.add('selected');
        selectedCity = btn.dataset.city;
        searchInput.value = btn.textContent;
        confirmBtn.disabled = false;
        suggestions.style.display = 'none';
      });
    });

    // 搜索功能
    let searchTimeout;
    searchInput.addEventListener('input', (e) => {
      const query = e.target.value.trim();

      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        if (query.length >= 2) {
          this.searchCities(query, suggestions);
        } else {
          suggestions.style.display = 'none';
        }
      }, 300);
    });

    // 确认选择
    confirmBtn.addEventListener('click', () => {
      if (selectedCity) {
        this.setManualCity(selectedCity);
        this.hideLocationSelector();
      }
    });

    // 取消选择
    cancelBtn.addEventListener('click', () => {
      this.hideLocationSelector();
      this.setDefaultCity(); // 使用默认城市
    });

    // 关闭按钮
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.hideLocationSelector();
        this.setDefaultCity();
      });
    }

    // 点击背景关闭
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        this.hideLocationSelector();
        this.setDefaultCity();
      }
    });
  }

  // 显示位置选择器
  showLocationSelector() {
    const modal = document.getElementById('location-selector-modal');
    if (modal) {
      modal.style.display = 'block';
      console.log('📍 显示手动位置选择器');

      // 清空之前的选择
      this.resetLocationSelector();

      // 聚焦搜索框
      setTimeout(() => {
        const searchInput = document.getElementById('city-search-input');
        if (searchInput) {
          searchInput.focus();
          // 移动端自动弹出键盘
          if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
            searchInput.click();
          }
        }
      }, 200);

      // 显示当前位置
      this.showCurrentLocationInSelector();
    } else {
      console.warn('⚠️ 位置选择器模态框未找到');
    }
  }

  // 重置位置选择器状态
  resetLocationSelector() {
    const searchInput = document.getElementById('city-search-input');
    const suggestions = document.getElementById('city-suggestions');
    const confirmBtn = document.getElementById('location-confirm-btn');
    const cityBtns = document.querySelectorAll('.city-btn');

    if (searchInput) searchInput.value = '';
    if (suggestions) suggestions.style.display = 'none';
    if (confirmBtn) confirmBtn.disabled = true;

    cityBtns.forEach(btn => btn.classList.remove('selected'));
  }

  // 在选择器中显示当前位置
  showCurrentLocationInSelector() {
    if (this.city && this.city !== '北京市') {
      const searchInput = document.getElementById('city-search-input');
      if (searchInput) {
        searchInput.placeholder = `当前位置：${this.city}，点击更改`;
      }

      // 如果当前城市在热门城市中，高亮显示
      const cityBtns = document.querySelectorAll('.city-btn');
      cityBtns.forEach(btn => {
        if (btn.dataset.city === this.city) {
          btn.style.background = '#e3f2fd';
          btn.style.borderColor = '#2196f3';
          btn.style.color = '#2196f3';
        }
      });
    }
  }

  // 隐藏位置选择器
  hideLocationSelector() {
    const modal = document.getElementById('location-selector-modal');
    if (modal) {
      modal.style.display = 'none';
    }
  }

  // 搜索城市
  async searchCities(query, suggestionsEl) {
    try {
      // 中国主要城市列表
      const cities = [
        '北京市', '上海市', '天津市', '重庆市',
        '广州市', '深圳市', '珠海市', '汕头市', '佛山市', '韶关市', '湛江市', '肇庆市', '江门市', '茂名市', '惠州市', '梅州市', '汕尾市', '河源市', '阳江市', '清远市', '东莞市', '中山市', '潮州市', '揭阳市', '云浮市',
        '南京市', '无锡市', '徐州市', '常州市', '苏州市', '南通市', '连云港市', '淮安市', '盐城市', '扬州市', '镇江市', '泰州市', '宿迁市',
        '杭州市', '宁波市', '温州市', '嘉兴市', '湖州市', '绍兴市', '金华市', '衢州市', '舟山市', '台州市', '丽水市',
        '合肥市', '芜湖市', '蚌埠市', '淮南市', '马鞍山市', '淮北市', '铜陵市', '安庆市', '黄山市', '滁州市', '阜阳市', '宿州市', '六安市', '亳州市', '池州市', '宣城市',
        '福州市', '厦门市', '莆田市', '三明市', '泉州市', '漳州市', '南平市', '龙岩市', '宁德市',
        '南昌市', '景德镇市', '萍乡市', '九江市', '新余市', '鹰潭市', '赣州市', '吉安市', '宜春市', '抚州市', '上饶市',
        '济南市', '青岛市', '淄博市', '枣庄市', '东营市', '烟台市', '潍坊市', '济宁市', '泰安市', '威海市', '日照市', '临沂市', '德州市', '聊城市', '滨州市', '菏泽市',
        '郑州市', '开封市', '洛阳市', '平顶山市', '安阳市', '鹤壁市', '新乡市', '焦作市', '濮阳市', '许昌市', '漯河市', '三门峡市', '南阳市', '商丘市', '信阳市', '周口市', '驻马店市',
        '武汉市', '黄石市', '十堰市', '宜昌市', '襄阳市', '鄂州市', '荆门市', '孝感市', '荆州市', '黄冈市', '咸宁市', '随州市',
        '长沙市', '株洲市', '湘潭市', '衡阳市', '邵阳市', '岳阳市', '常德市', '张家界市', '益阳市', '郴州市', '永州市', '怀化市', '娄底市',
        '成都市', '自贡市', '攀枝花市', '泸州市', '德阳市', '绵阳市', '广元市', '遂宁市', '内江市', '乐山市', '南充市', '眉山市', '宜宾市', '广安市', '达州市', '雅安市', '巴中市', '资阳市',
        '昆明市', '曲靖市', '玉溪市', '保山市', '昭通市', '丽江市', '普洱市', '临沧市',
        '西安市', '铜川市', '宝鸡市', '咸阳市', '渭南市', '延安市', '汉中市', '榆林市', '安康市', '商洛市'
      ];

      const filtered = cities.filter(city =>
        city.includes(query) || city.replace('市', '').includes(query)
      ).slice(0, 10);

      if (filtered.length > 0) {
        suggestionsEl.innerHTML = filtered.map(city =>
          `<div class="city-suggestion" data-city="${city}">${city}</div>`
        ).join('');

        suggestionsEl.style.display = 'block';

        // 添加点击事件
        suggestionsEl.querySelectorAll('.city-suggestion').forEach(item => {
          item.addEventListener('click', () => {
            const city = item.dataset.city;
            document.getElementById('city-search-input').value = city.replace('市', '');
            document.querySelectorAll('.city-btn').forEach(btn => btn.classList.remove('selected'));
            document.getElementById('location-confirm-btn').disabled = false;
            suggestionsEl.style.display = 'none';

            // 设置选中的城市
            const confirmBtn = document.getElementById('location-confirm-btn');
            confirmBtn.onclick = () => {
              this.setManualCity(city);
              this.hideLocationSelector();
            };
          });
        });
      } else {
        suggestionsEl.style.display = 'none';
      }
    } catch (error) {
      console.warn('⚠️ 城市搜索失败:', error);
    }
  }

  // 设置手动选择的城市
  setManualCity(city) {
    this.city = city;
    this.locationObtained = true;
    this.locationMethod = 'manual';
    console.log('✅ 手动选择城市:', this.city);

    this.saveData();
    this.notifySubscribers();

    // 立即获取天气数据
    this.getWeatherData();
  }

  // 获取当前数据（同步方法）
  getCurrentData() {
    return {
      city: this.city,
      weather: this.weatherData,
      timestamp: this.lastUpdateTime,
      isExpired: this.isDataExpired(),
      locationMethod: this.locationMethod,
      locationAttempts: this.locationAttempts,
      apiStats: {
        todayCount: this.apiCallCount,
        dailyLimit: this.dailyLimit,
        dailyPercentage: (this.apiCallCount / this.dailyLimit * 100).toFixed(1),
        monthlyCount: this.monthlyCallCount,
        monthlyLimit: this.monthlyLimit,
        monthlyPercentage: (this.monthlyCallCount / this.monthlyLimit * 100).toFixed(1)
      }
    };
  }
}

// 确保在全局作用域中可用
window.WeatherManager = WeatherManager;

// 不在这里创建实例，由配置文件统一管理
