---
title: {{ title }}
cover: 
keywords: []
date: {{ date }}
tags: []
categories: []
description: 
---

<style>
.terminal-block {
    background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
    border-radius: 8px;
    margin: 15px 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    font-family: 'Courier New', monospace;
}

.terminal-header {
    background: linear-gradient(90deg, #3a3a3a 0%, #4a4a4a 100%);
    padding: 8px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #555;
}

.terminal-title {
    color: #ffffff;
    font-size: 12px;
    font-weight: bold;
}

.terminal-buttons {
    display: flex;
    gap: 6px;
}

.terminal-btn {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
}

.terminal-btn.close { background: #ff5f57; }
.terminal-btn.minimize { background: #ffbd2e; }
.terminal-btn.maximize { background: #28ca42; }

.terminal-content {
    padding: 15px;
    color: #00ff00;
    font-size: 14px;
    position: relative;
    background: #1e1e1e;
}

.terminal-prompt {
    color: #00ff00;
    margin-right: 8px;
}

.copy-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: #007acc;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: background 0.3s;
}

.copy-btn:hover {
    background: #005a9e;
}
</style>

<script>
function copyTerminalContent(text, button) {
    navigator.clipboard.writeText(text).then(function() {
        const originalText = button.textContent;
        button.textContent = '已复制';
        button.style.background = '#28a745';
        setTimeout(function() {
            button.textContent = originalText;
            button.style.background = '#007acc';
        }, 2000);
    }).catch(function(err) {
        console.error('复制失败: ', err);
    });
}
</script>

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">Terminal Title</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        https://example.com
        <button class="copy-btn" onclick="copyTerminalContent('https://example.com', this)">复制</button>
    </div>
</div>

// 注: 终端块仅用于链接、账号、密码等便于复制的内容。
