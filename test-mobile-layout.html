<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>移动端布局测试</title>
    <link rel="stylesheet" href="source/css/custom.css">
    <link rel="stylesheet" href="source/css/mobile-fix-gentle.css">
    <style>
        /* 基础样式模拟 */
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .layout {
            background: white;
            min-height: 100vh;
        }
        
        .main-content {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        #aside-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px;
        }
        
        .card-widget {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .card-info {
            border-left: 4px solid #007bff;
        }
        
        .card-clock {
            border-left: 4px solid #28a745;
        }
        
        .test-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
        
        .content-text {
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .highlight-box {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <div>屏幕宽度: <span id="screen-width"></span>px</div>
        <div>布局模式: <span id="layout-mode"></span></div>
    </div>

    <div class="layout">
        <!-- 主内容区域 -->
        <div class="main-content">
            <h1>移动端布局测试</h1>
            <div class="content-text">
                这是主要内容区域。在移动端（屏幕宽度 ≤ 768px）时，这个区域应该占据全宽，不会被侧边栏压缩。
            </div>
            
            <div class="highlight-box">
                <strong>测试要点：</strong>
                <ul>
                    <li>在桌面端：主内容和侧边栏并排显示</li>
                    <li>在移动端：主内容全宽，侧边栏隐藏或移到底部</li>
                    <li>文章内容不应该向左偏移</li>
                    <li>侧边栏时钟应该完全隐藏</li>
                </ul>
            </div>
            
            <div class="content-text">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
            </div>
            
            <div class="content-text">
                Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
            </div>
        </div>
        
        <!-- 侧边栏 -->
        <div id="aside-content">
            <div class="card-widget card-info">
                <h3>信息卡片</h3>
                <p>这是信息卡片，在移动端应该隐藏或移到底部。</p>
            </div>
            
            <div class="card-widget card-clock">
                <h3>时钟组件</h3>
                <p>这是侧边栏时钟，在移动端应该完全隐藏。</p>
            </div>
            
            <div class="card-widget">
                <h3>其他组件</h3>
                <p>这是其他侧边栏组件，在移动端也应该隐藏。</p>
            </div>
        </div>
    </div>

    <script>
        function updateScreenInfo() {
            const width = window.innerWidth;
            const isMobile = width <= 768;
            
            document.getElementById('screen-width').textContent = width;
            document.getElementById('layout-mode').textContent = isMobile ? '移动端' : '桌面端';
            
            // 添加调试信息
            console.log('屏幕宽度:', width);
            console.log('是否移动端:', isMobile);
            
            // 检查布局
            const layout = document.querySelector('.layout');
            const mainContent = document.querySelector('.main-content');
            const asideContent = document.querySelector('#aside-content');
            
            if (layout && mainContent && asideContent) {
                const layoutStyle = window.getComputedStyle(layout);
                const mainStyle = window.getComputedStyle(mainContent);
                const asideStyle = window.getComputedStyle(asideContent);
                
                console.log('Layout display:', layoutStyle.display);
                console.log('Layout flex-direction:', layoutStyle.flexDirection);
                console.log('Main content width:', mainStyle.width);
                console.log('Aside display:', asideStyle.display);
            }
        }
        
        // 初始化
        updateScreenInfo();
        
        // 监听窗口大小变化
        window.addEventListener('resize', updateScreenInfo);
        
        // 主题切换测试
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            html.setAttribute('data-theme', newTheme);
        }
        
        // 添加主题切换按钮
        const themeBtn = document.createElement('button');
        themeBtn.textContent = '切换主题';
        themeBtn.style.cssText = 'position: fixed; bottom: 20px; right: 20px; padding: 10px; z-index: 1000;';
        themeBtn.onclick = toggleTheme;
        document.body.appendChild(themeBtn);
    </script>
</body>
</html>
