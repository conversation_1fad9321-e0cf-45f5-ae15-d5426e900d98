<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>容器选择逻辑测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="source/css/banner-clock.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        [data-theme="dark"] body {
            background: #1a1a1a;
            color: #e2e8f0;
        }
        
        /* 模拟页面结构 */
        #body-wrap {
            min-height: 100vh;
            position: relative;
        }
        
        #page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        #banners {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            color: white;
            margin: 20px;
            max-width: 600px;
        }
        
        .banners-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .banners-subtitle {
            font-size: 1.2rem;
            opacity: 0.8;
        }
        
        /* 控制面板 */
        .control-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            z-index: 10000;
            min-width: 250px;
        }
        
        [data-theme="dark"] .control-panel {
            background: #2d3748;
            color: #e2e8f0;
        }
        
        .control-section {
            margin-bottom: 20px;
        }
        
        .control-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        [data-theme="dark"] .control-title {
            color: #e2e8f0;
        }
        
        .control-btn {
            display: block;
            width: 100%;
            padding: 8px 12px;
            margin: 5px 0;
            border: 1px solid #007bff;
            background: #007bff;
            color: white;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            text-align: center;
            text-decoration: none;
        }
        
        .control-btn:hover {
            background: #0056b3;
        }
        
        .control-btn.danger {
            background: #dc3545;
            border-color: #dc3545;
        }
        
        .control-btn.success {
            background: #28a745;
            border-color: #28a745;
        }
        
        .status-display {
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            font-size: 11px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        
        [data-theme="dark"] .status-display {
            background: #1a202c;
        }
        
        /* 容器状态指示器 */
        .container-indicator {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #28a745;
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 10000;
        }
        
        /* 隐藏容器的样式 */
        .hide-banners #banners { display: none !important; }
        .hide-page-header #page-header { display: none !important; }
        .hide-body-wrap #body-wrap { display: none !important; }
        
        /* 主内容区域 */
        .main-content {
            padding: 40px 20px;
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            margin-top: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        [data-theme="dark"] .main-content {
            background: #2d3748;
        }
    </style>
</head>
<body id="body" data-type="solitude">
    <!-- 容器状态指示器 -->
    <div class="container-indicator" id="container-indicator">容器状态检测中...</div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
        <div class="control-section">
            <div class="control-title">🎮 页面模式</div>
            <button class="control-btn" onclick="showHomePage()">首页模式</button>
            <button class="control-btn" onclick="showArticlePage()">文章页模式</button>
            <button class="control-btn" onclick="showToolPage()">工具页模式</button>
            <button class="control-btn" onclick="showMinimalPage()">最小页面模式</button>
        </div>
        
        <div class="control-section">
            <div class="control-title">🔧 容器控制</div>
            <button class="control-btn" onclick="toggleBanners()">切换banners</button>
            <button class="control-btn" onclick="togglePageHeader()">切换page-header</button>
            <button class="control-btn" onclick="toggleBodyWrap()">切换body-wrap</button>
        </div>
        
        <div class="control-section">
            <div class="control-title">⚙️ 时钟控制</div>
            <button class="control-btn success" onclick="initClock()">初始化时钟</button>
            <button class="control-btn danger" onclick="destroyClock()">销毁时钟</button>
            <button class="control-btn" onclick="toggleTheme()">切换主题</button>
        </div>
        
        <div class="control-section">
            <div class="control-title">📊 状态信息</div>
            <div class="status-display" id="status-display">状态加载中...</div>
        </div>
    </div>
    
    <!-- 页面结构 -->
    <div id="body-wrap">
        <header id="page-header">
            <div id="banners">
                <div class="banners-title">欢迎来到我的博客</div>
                <div class="banners-subtitle">测试容器选择逻辑</div>
            </div>
        </header>
        
        <main class="main-content">
            <h2>容器选择逻辑测试</h2>
            <p>这个页面用于测试横幅时钟组件的智能容器选择逻辑。</p>
            <p>时钟组件会根据页面结构自动选择最合适的容器：</p>
            <ol>
                <li><strong>banners容器</strong> - 首页模式，时钟在横幅内</li>
                <li><strong>page-header容器</strong> - 文章页模式，时钟在页面头部</li>
                <li><strong>body-wrap容器</strong> - 工具页模式，时钟在页面包装内</li>
                <li><strong>body容器</strong> - 最小页面模式，时钟在body内</li>
            </ol>
            <p>使用右侧控制面板测试不同的页面模式和容器组合。</p>
        </main>
    </div>

    <script>
        // 从配置文件复制的优化后的初始化函数
        function initBannerClock() {
            console.log('🎯 开始初始化横幅时钟...');
            console.log('🔍 当前页面URL:', window.location.href);
            
            // 清理旧实例
            if (window.bannerClockInstance) {
                if (window.bannerClockInstance.destroy) {
                    window.bannerClockInstance.destroy();
                }
                window.bannerClockInstance = null;
                console.log('🗑️ 清理旧的横幅时钟实例');
            }

            // 移除旧DOM
            const existingClock = document.getElementById('banner-clock');
            if (existingClock) {
                existingClock.remove();
                console.log('🗑️ 移除旧的横幅时钟DOM');
            }

            // 等待DOM稳定
            setTimeout(() => {
                // 智能容器选择策略
                let targetContainer = null;
                let containerType = 'fixed';
                
                // 检查各种可能的容器
                const bannersContainer = document.getElementById('banners');
                const pageHeader = document.getElementById('page-header');
                const bodyWrap = document.getElementById('body-wrap');
                
                console.log('🔍 DOM检查结果:');
                console.log('- banners容器:', bannersContainer ? '✅ 存在' : '❌ 不存在');
                console.log('- page-header:', pageHeader ? '✅ 存在' : '❌ 不存在');
                console.log('- body-wrap:', bodyWrap ? '✅ 存在' : '❌ 不存在');
                console.log('- body元素:', document.body ? '✅ 存在' : '❌ 不存在');
                
                // 容器选择优先级
                if (bannersContainer && bannersContainer.offsetParent !== null) {
                    targetContainer = bannersContainer;
                    containerType = 'banners';
                    console.log('📍 使用banners容器（首页模式）');
                } else if (pageHeader && pageHeader.offsetParent !== null) {
                    targetContainer = pageHeader;
                    containerType = 'page-header';
                    console.log('📍 使用page-header容器（页面头部模式）');
                } else if (bodyWrap && bodyWrap.offsetParent !== null) {
                    targetContainer = bodyWrap;
                    containerType = 'body-wrap';
                    console.log('📍 使用body-wrap容器（页面包装模式）');
                } else {
                    targetContainer = document.body;
                    containerType = 'body';
                    console.log('📍 使用body容器（兜底模式）');
                }
                
                if (!targetContainer) {
                    console.error('❌ 无法找到合适的容器，跳过时钟初始化');
                    return;
                }

                // 创建时钟DOM
                const bannerClock = document.createElement('div');
                bannerClock.id = 'banner-clock';
                bannerClock.setAttribute('data-container', containerType);
                bannerClock.innerHTML = `
                    <div class="banner-clock-time">测试模式</div>
                    <div class="banner-clock-date">${new Date().toLocaleDateString()}</div>
                    <div class="banner-clock-weather">
                        <div class="banner-clock-weather-item">
                            <i class="fas fa-cog"></i>
                            <span>${containerType}</span>
                        </div>
                    </div>
                    <div class="banner-clock-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>容器测试</span>
                    </div>
                `;
                
                targetContainer.appendChild(bannerClock);
                console.log(`📍 横幅时钟DOM已创建并添加到${containerType}容器`);

                // 模拟组件初始化
                setTimeout(() => {
                    window.bannerClockInstance = {
                        destroy: () => console.log('🗑️ 模拟组件销毁'),
                        containerType: containerType
                    };
                    console.log('🚀 横幅时钟组件已初始化');
                    
                    // 模拟时间更新
                    setInterval(() => {
                        const timeEl = bannerClock.querySelector('.banner-clock-time');
                        if (timeEl) {
                            timeEl.textContent = new Date().toLocaleTimeString();
                        }
                    }, 1000);
                }, 300);
            }, 100);
        }
        
        // 页面模式控制
        function showHomePage() {
            document.body.className = '';
            console.log('🏠 切换到首页模式');
            setTimeout(initClock, 100);
        }
        
        function showArticlePage() {
            document.body.className = 'hide-banners';
            console.log('📄 切换到文章页模式');
            setTimeout(initClock, 100);
        }
        
        function showToolPage() {
            document.body.className = 'hide-banners hide-page-header';
            console.log('🔧 切换到工具页模式');
            setTimeout(initClock, 100);
        }
        
        function showMinimalPage() {
            document.body.className = 'hide-banners hide-page-header hide-body-wrap';
            console.log('📋 切换到最小页面模式');
            setTimeout(initClock, 100);
        }
        
        // 容器控制
        function toggleBanners() {
            document.body.classList.toggle('hide-banners');
            console.log('🔄 切换banners容器');
            setTimeout(initClock, 100);
        }
        
        function togglePageHeader() {
            document.body.classList.toggle('hide-page-header');
            console.log('🔄 切换page-header容器');
            setTimeout(initClock, 100);
        }
        
        function toggleBodyWrap() {
            document.body.classList.toggle('hide-body-wrap');
            console.log('🔄 切换body-wrap容器');
            setTimeout(initClock, 100);
        }
        
        // 时钟控制
        function initClock() {
            initBannerClock();
        }
        
        function destroyClock() {
            if (window.bannerClockInstance) {
                window.bannerClockInstance.destroy();
                window.bannerClockInstance = null;
            }
            const clock = document.getElementById('banner-clock');
            if (clock) {
                clock.remove();
            }
            console.log('💥 时钟已销毁');
        }
        
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            console.log('🎨 主题切换到:', newTheme);
        }
        
        // 状态更新
        function updateStatus() {
            const clock = document.getElementById('banner-clock');
            const banners = document.getElementById('banners');
            const pageHeader = document.getElementById('page-header');
            const bodyWrap = document.getElementById('body-wrap');
            
            let status = '';
            status += `时钟存在: ${clock ? '✅' : '❌'}\n`;
            status += `banners可见: ${banners && banners.offsetParent !== null ? '✅' : '❌'}\n`;
            status += `page-header可见: ${pageHeader && pageHeader.offsetParent !== null ? '✅' : '❌'}\n`;
            status += `body-wrap可见: ${bodyWrap && bodyWrap.offsetParent !== null ? '✅' : '❌'}\n`;
            
            if (clock) {
                const containerType = clock.getAttribute('data-container');
                const parent = clock.parentElement;
                status += `容器类型: ${containerType}\n`;
                status += `父容器: ${parent.tagName}${parent.id ? '#' + parent.id : ''}\n`;
            }
            
            status += `实例存在: ${window.bannerClockInstance ? '✅' : '❌'}\n`;
            status += `时间: ${new Date().toLocaleTimeString()}`;
            
            document.getElementById('status-display').textContent = status;
            
            // 更新容器指示器
            const indicator = document.getElementById('container-indicator');
            if (clock) {
                const containerType = clock.getAttribute('data-container');
                indicator.textContent = `当前容器: ${containerType}`;
                indicator.style.background = '#28a745';
            } else {
                indicator.textContent = '时钟未初始化';
                indicator.style.background = '#dc3545';
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 页面加载完成，开始测试');
            initBannerClock();
            
            // 定期更新状态
            setInterval(updateStatus, 1000);
        });
    </script>
</body>
</html>
